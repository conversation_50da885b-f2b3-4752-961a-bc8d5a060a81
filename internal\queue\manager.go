package queue

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"walmart-bind-card-processor/internal/config"
	"walmart-bind-card-processor/internal/model"

	"github.com/sirupsen/logrus"
	"github.com/streadway/amqp"
)

// Manager 消息队列管理器
type Manager struct {
	conn    *amqp.Connection
	channel *amqp.Channel
	config  config.RabbitMQConfig
	logger  *logrus.Logger
	closed  bool
}

// Message 消息结构体
type Message struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	Retry     int                    `json:"retry"`
}

// BindCardMessage 绑卡消息结构体，与Python系统完全兼容
type BindCardMessage struct {
	RecordID        string                 `json:"record_id"`
	MerchantID      uint                   `json:"merchant_id"`
	DepartmentID    *uint                  `json:"department_id"`
	WalmartCKID     *uint                  `json:"walmart_ck_id"`
	CardNumber      string                 `json:"card_number"`
	CardPassword    string                 `json:"card_password"`
	Amount          int                    `json:"amount"`
	MerchantOrderID string                 `json:"merchant_order_id"`
	ExtData         *string                `json:"ext_data"`
	IsTestMode      bool                   `json:"is_test_mode"`
	Debug           bool                   `json:"debug"`
	RequestData     map[string]interface{} `json:"request_data"`
	CreatedAt       time.Time              `json:"created_at"`
}

// CallbackMessage 回调消息结构体
type CallbackMessage struct {
	RecordID       string                 `json:"record_id"`
	MerchantID     uint                   `json:"merchant_id"`
	Status         string                 `json:"status"`
	Result         map[string]interface{} `json:"result"`
	CallbackURL    string                 `json:"callback_url"`
	RetryCount     int                    `json:"retry_count"`
	MaxRetries     int                    `json:"max_retries"`
	NextRetryTime  *time.Time             `json:"next_retry_time"`
}

// NewManager 创建新的队列管理器
func NewManager(cfg config.RabbitMQConfig, logger *logrus.Logger) (*Manager, error) {
	conn, err := amqp.Dial(cfg.GetRabbitMQURL())
	if err != nil {
		return nil, fmt.Errorf("无法连接到RabbitMQ: %w", err)
	}

	channel, err := conn.Channel()
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("无法打开通道: %w", err)
	}

	// 设置QoS
	if err := channel.Qos(cfg.ConsumerPrefetchCount, 0, false); err != nil {
		channel.Close()
		conn.Close()
		return nil, fmt.Errorf("无法设置 QoS: %w", err)
	}

	manager := &Manager{
		conn:    conn,
		channel: channel,
		config:  cfg,
		logger:  logger,
		closed:  false,
	}

	// 声明队列
	if err := manager.declareQueues(); err != nil {
		manager.Close()
		return nil, fmt.Errorf("无法声明队列: %w", err)
	}

	// 监听连接关闭事件
	go manager.handleConnectionClose()

	return manager, nil
}

// declareQueues 声明队列
func (m *Manager) declareQueues() error {
	// 检查是否启用严格模式
	if m.config.QueueManagement.StrictMode {
		m.logger.Info("严格模式已启用，跳过队列声明以避免创建临时队列")
		return m.checkQueueExistence()
	}

	// 检查是否允许自动创建队列
	if !m.config.QueueManagement.AutoCreateQueues {
		m.logger.Info("自动创建队列已禁用，跳过队列声明")
		if m.config.QueueManagement.CheckQueueExistence {
			return m.checkQueueExistence()
		}
		return nil
	}

	// 声明绑卡队列（禁止使用死信队列，绑卡任务必须完成）
	_, err := m.channel.QueueDeclare(
		m.config.QueueBindCard, // 队列名
		true,                   // 持久化
		false,                  // 不自动删除
		false,                  // 不排他
		false,                  // 不等待
		nil,                    // 不设置任何额外参数，禁止死信队列
	)
	if err != nil {
		return fmt.Errorf("无法声明绑卡队列: %w", err)
	}

	// 声明回调队列（禁止使用死信队列）
	_, err = m.channel.QueueDeclare(
		m.config.QueueCallback, // 队列名
		true,                   // 持久化
		false,                  // 不自动删除
		false,                  // 不排他
		false,                  // 不等待
		nil,                    // 不设置任何额外参数，禁止死信队列
	)
	if err != nil {
		return fmt.Errorf("无法声明回调队列: %w", err)
	}

	// 声明金额查询队列（禁止使用死信队列）
	if queueName, exists := m.config.Queues["card_balance_query"]; exists {
		_, err = m.channel.QueueDeclare(
			queueName, // 队列名
			true,      // 持久化
			false,     // 不自动删除
			false,     // 不排他
			false,     // 不等待
			nil,       // 不设置任何额外参数，禁止死信队列
		)
		if err != nil {
			return fmt.Errorf("无法声明金额查询队列: %w", err)
		}
	}

	// 声明重试队列（禁止使用死信队列）
	if queueName, exists := m.config.Queues["retry"]; exists {
		_, err = m.channel.QueueDeclare(
			queueName, // 队列名
			true,      // 持久化
			false,     // 不自动删除
			false,     // 不排他
			false,     // 不等待
			nil,       // 不设置任何额外参数，禁止死信队列
		)
		if err != nil {
			return fmt.Errorf("无法声明重试队列: %w", err)
		}
	}

	// 队列声明完成，禁止使用死信队列
	// 绑卡任务必须完成，不允许消息丢失
	return nil
}

// checkQueueExistence 检查队列是否存在
func (m *Manager) checkQueueExistence() error {
	// 检查绑卡队列是否存在
	_, err := m.channel.QueueInspect(m.config.QueueBindCard)
	if err != nil {
		return fmt.Errorf("绑卡队列 '%s' 不存在，请先在RabbitMQ管理界面创建: %w", m.config.QueueBindCard, err)
	}
	m.logger.Infof("绑卡队列 '%s' 存在性检查通过", m.config.QueueBindCard)

	// 检查回调队列是否存在
	_, err = m.channel.QueueInspect(m.config.QueueCallback)
	if err != nil {
		m.logger.Warnf("回调队列 '%s' 不存在，但不影响绑卡功能: %v", m.config.QueueCallback, err)
	} else {
		m.logger.Infof("回调队列 '%s' 存在性检查通过", m.config.QueueCallback)
	}

	// 检查其他配置的队列
	for queueType, queueName := range m.config.Queues {
		_, err := m.channel.QueueInspect(queueName)
		if err != nil {
			m.logger.Warnf("队列 '%s' (%s) 不存在: %v", queueName, queueType, err)
		} else {
			m.logger.Infof("队列 '%s' (%s) 存在性检查通过", queueName, queueType)
		}
	}

	return nil
}

// PublishBindCard 发布绑卡消息
func (m *Manager) PublishBindCard(ctx context.Context, msg *BindCardMessage) error {
	if m.closed {
		return fmt.Errorf("queue manager is closed")
	}

	body, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("无法序列化消息: %w", err)
	}

	return m.channel.Publish(
		"",                     // 交换机
		m.config.QueueBindCard, // 路由键
		false,                  // 不强制
		false,                  // 不立即
		amqp.Publishing{
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp.Persistent, // 持久化消息
			Timestamp:    time.Now(),
			MessageId:    msg.RecordID,
		},
	)
}

// PublishCallback 发布回调消息
func (m *Manager) PublishCallback(ctx context.Context, msg *CallbackMessage) error {
	if m.closed {
		return fmt.Errorf("queue manager is closed")
	}

	body, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("无法序列化消息: %w", err)
	}

	// 计算延迟时间
	var delay time.Duration
	if msg.NextRetryTime != nil {
		delay = time.Until(*msg.NextRetryTime)
		if delay < 0 {
			delay = 0
		}
	}

	headers := amqp.Table{}
	if delay > 0 {
		headers["x-delay"] = int64(delay.Milliseconds())
	}

	return m.channel.Publish(
		"",                     // 交换机
		m.config.QueueCallback, // 路由键
		false,                  // 不强制
		false,                  // 不立即
		amqp.Publishing{
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp.Persistent, // 持久化消息
			Timestamp:    time.Now(),
			MessageId:    msg.RecordID,
			Headers:      headers,
		},
	)
}

// PublishBalanceQuery 发布金额查询消息
func (m *Manager) PublishBalanceQuery(ctx context.Context, msg *model.BalanceQueryMessage) error {
	if m.closed {
		return fmt.Errorf("queue manager is closed")
	}

	queueName, exists := m.config.Queues["card_balance_query"]
	if !exists {
		return fmt.Errorf("card balance query queue not configured")
	}

	body, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("无法序列化金额查询消息: %w", err)
	}

	return m.channel.Publish(
		"",        // 交换机
		queueName, // 路由键
		false,     // 不强制
		false,     // 不立即
		amqp.Publishing{
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp.Persistent, // 持久化消息
			Timestamp:    time.Now(),
			MessageId:    msg.RecordID,
		},
	)
}

// PublishRetry 发布重试消息
func (m *Manager) PublishRetry(ctx context.Context, msg *model.RetryMessage) error {
	if m.closed {
		return fmt.Errorf("queue manager is closed")
	}

	queueName, exists := m.config.Queues["retry"]
	if !exists {
		return fmt.Errorf("retry queue not configured")
	}

	body, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("无法序列化重试消息: %w", err)
	}

	// 计算延迟时间
	delay := time.Until(msg.NextRetryAt)
	if delay < 0 {
		delay = 0
	}

	headers := amqp.Table{}
	if delay > 0 {
		headers["x-delay"] = int64(delay.Milliseconds())
	}

	return m.channel.Publish(
		"",        // 交换机
		queueName, // 路由键
		false,     // 不强制
		false,     // 不立即
		amqp.Publishing{
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp.Persistent, // 持久化消息
			Timestamp:    time.Now(),
			MessageId:    msg.RecordID,
			Headers:      headers,
		},
	)
}

// ConsumeBindCard 消费绑卡消息
func (m *Manager) ConsumeBindCard(ctx context.Context, handler func(*BindCardMessage) error) error {
	if m.closed {
		return fmt.Errorf("queue manager is closed")
	}

	msgs, err := m.channel.Consume(
		m.config.QueueBindCard, // 队列名
		"bind_card_consumer",   // 消费者标签
		false,                  // 不自动确认
		false,                  // 不排他
		false,                  // 不等待
		false,                  // 不本地
		nil,
	)
	if err != nil {
		return fmt.Errorf("无法注册消费者: %w", err)
	}

	// 启动多个消费者协程
	for i := 0; i < m.config.ConsumerConcurrency; i++ {
		go func(workerID int) {
			m.logger.Infof("Starting bind card consumer worker %d", workerID)
			for {
				select {
				case <-ctx.Done():
					m.logger.Infof("Stopping bind card consumer worker %d", workerID)
					return
				case delivery, ok := <-msgs:
					if !ok {
						m.logger.Warnf("Bind card consumer worker %d: channel closed", workerID)
						return
					}

					// 处理消息
					if err := m.handleBindCardMessage(delivery, handler); err != nil {
						m.logger.Errorf("Worker %d failed to handle bind card message: %v", workerID, err)
					}
				}
			}
		}(i)
	}

	return nil
}

// handleBindCardMessage 处理绑卡消息
func (m *Manager) handleBindCardMessage(delivery amqp.Delivery, handler func(*BindCardMessage) error) error {
	var msg BindCardMessage
	if err := json.Unmarshal(delivery.Body, &msg); err != nil {
		m.logger.Errorf("Failed to unmarshal bind card message: %v, raw body: %s", err, string(delivery.Body))
		// 绑卡消息格式错误也要重新入队，避免数据丢失，需要人工处理
		delivery.Nack(false, true) // 拒绝消息，重新入队等待修复
		return err
	}

	// 调用处理器
	if err := handler(&msg); err != nil {
		m.logger.Errorf("Handler failed for bind card message %s: %v", msg.RecordID, err)
		delivery.Nack(false, true) // 拒绝消息，重新入队
		return err
	}

	// 确认消息
	delivery.Ack(false)
	return nil
}

// handleConnectionClose 处理连接关闭事件
func (m *Manager) handleConnectionClose() {
	closeError := <-m.conn.NotifyClose(make(chan *amqp.Error))
	if closeError != nil {
		m.logger.Errorf("RabbitMQ connection closed: %v", closeError)
	}
	m.closed = true
}

// Close 关闭队列管理器
func (m *Manager) Close() error {
	if m.closed {
		return nil
	}

	m.closed = true

	if m.channel != nil {
		if err := m.channel.Close(); err != nil {
			m.logger.Errorf("Failed to close channel: %v", err)
		}
	}

	if m.conn != nil {
		if err := m.conn.Close(); err != nil {
			m.logger.Errorf("Failed to close connection: %v", err)
		}
	}

	return nil
}

// IsHealthy 检查队列管理器健康状态
func (m *Manager) IsHealthy() bool {
	return !m.closed && m.conn != nil && !m.conn.IsClosed()
}

// ConsumeBalanceQuery 消费金额查询消息
func (m *Manager) ConsumeBalanceQuery(ctx context.Context, handler func(*model.BalanceQueryMessage) error) error {
	if m.closed {
		return fmt.Errorf("queue manager is closed")
	}

	queueName, exists := m.config.Queues["card_balance_query"]
	if !exists {
		return fmt.Errorf("card balance query queue not configured")
	}

	msgs, err := m.channel.Consume(
		queueName,                  // 队列名
		"balance_query_consumer",   // 消费者标签
		false,                      // 不自动确认
		false,                      // 不排他
		false,                      // 不等待
		false,                      // 不本地
		nil,
	)
	if err != nil {
		return fmt.Errorf("无法注册金额查询消费者: %w", err)
	}

	// 启动消费者协程
	go func() {
		m.logger.Info("Starting balance query consumer")
		for {
			select {
			case <-ctx.Done():
				m.logger.Info("Stopping balance query consumer")
				return
			case delivery, ok := <-msgs:
				if !ok {
					m.logger.Warn("Balance query consumer: channel closed")
					return
				}

				// 处理消息
				if err := m.handleBalanceQueryMessage(delivery, handler); err != nil {
					m.logger.Errorf("Failed to handle balance query message: %v", err)
				}
			}
		}
	}()

	return nil
}

// ConsumeRetry 消费重试消息
func (m *Manager) ConsumeRetry(ctx context.Context, handler func(*model.RetryMessage) error) error {
	if m.closed {
		return fmt.Errorf("queue manager is closed")
	}

	queueName, exists := m.config.Queues["retry"]
	if !exists {
		return fmt.Errorf("retry queue not configured")
	}

	msgs, err := m.channel.Consume(
		queueName,         // 队列名
		"retry_consumer",  // 消费者标签
		false,             // 不自动确认
		false,             // 不排他
		false,             // 不等待
		false,             // 不本地
		nil,
	)
	if err != nil {
		return fmt.Errorf("无法注册重试消费者: %w", err)
	}

	// 启动消费者协程
	go func() {
		m.logger.Info("Starting retry consumer")
		for {
			select {
			case <-ctx.Done():
				m.logger.Info("Stopping retry consumer")
				return
			case delivery, ok := <-msgs:
				if !ok {
					m.logger.Warn("Retry consumer: channel closed")
					return
				}

				// 处理消息
				if err := m.handleRetryMessage(delivery, handler); err != nil {
					m.logger.Errorf("Failed to handle retry message: %v", err)
				}
			}
		}
	}()

	return nil
}

// handleBalanceQueryMessage 处理金额查询消息
func (m *Manager) handleBalanceQueryMessage(delivery amqp.Delivery, handler func(*model.BalanceQueryMessage) error) error {
	var msg model.BalanceQueryMessage
	if err := json.Unmarshal(delivery.Body, &msg); err != nil {
		m.logger.Errorf("Failed to unmarshal balance query message: %v, raw body: %s", err, string(delivery.Body))
		// 金额查询消息格式错误也要重新入队，避免数据丢失，需要人工处理
		delivery.Nack(false, true) // 拒绝消息，重新入队等待修复
		return err
	}

	// 调用处理器
	if err := handler(&msg); err != nil {
		m.logger.Errorf("Handler failed for balance query message %s: %v", msg.RecordID, err)
		delivery.Nack(false, true) // 拒绝消息，重新入队
		return err
	}

	// 确认消息
	delivery.Ack(false)
	return nil
}

// handleRetryMessage 处理重试消息
func (m *Manager) handleRetryMessage(delivery amqp.Delivery, handler func(*model.RetryMessage) error) error {
	var msg model.RetryMessage
	if err := json.Unmarshal(delivery.Body, &msg); err != nil {
		m.logger.Errorf("Failed to unmarshal retry message: %v, raw body: %s", err, string(delivery.Body))
		// 重试消息格式错误也要重新入队，避免数据丢失，需要人工处理
		delivery.Nack(false, true) // 拒绝消息，重新入队等待修复
		return err
	}

	// 调用处理器
	if err := handler(&msg); err != nil {
		m.logger.Errorf("Handler failed for retry message %s: %v", msg.RecordID, err)
		delivery.Nack(false, true) // 拒绝消息，重新入队
		return err
	}

	// 确认消息
	delivery.Ack(false)
	return nil
}
