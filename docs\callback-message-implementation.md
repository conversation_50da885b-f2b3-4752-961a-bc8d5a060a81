# Go绑卡模块回调消息发送逻辑实现

## 概述

根据用户的严格要求，我们在Go语言绑卡模块中实现了完整的回调消息发送逻辑，确保在正确的时机向Python系统发送回调消息。

## 实现的功能

### 1. 绑卡成功场景的回调消息发送

**触发条件**（必须同时满足以下三个条件）：
- ✅ 绑卡API调用成功
- ✅ 获取卡片真实金额成功  
- ✅ 获取到的真实金额与商户传入的金额完全一致（单位：分）

**实现位置**：
- `updateCardBalanceRecord` 方法末尾调用 `validateAmountAndSendCallback`
- `validateAmountAndSendCallback` 方法使用 `AmountValidatorService` 进行金额验证

**代码流程**：
```go
// 1. 金额验证成功后发送成功回调
if err := p.amountValidator.ValidateAmount(ctx, msg.RecordID, msg.Amount, actualAmount); err != nil {
    // 金额验证失败，发送失败回调
} else {
    // 金额验证成功，发送成功回调
}
```

### 2. 金额验证失败场景的特殊处理

**触发条件**：
- ✅ 绑卡API调用成功
- ✅ 获取卡片真实金额成功
- ❌ 真实金额与商户金额不一致

**处理逻辑**：
- 使用 `AmountValidatorService.ValidateAmount` 进行验证
- 验证失败时自动更新数据库状态为 `failed`
- 发送失败回调消息，包含金额不匹配的详细信息

### 3. 获取金额失败的特殊处理

**触发条件**：
- ✅ 绑卡API调用成功
- ❌ 获取卡片真实金额失败

**处理逻辑**：
- **不发送任何回调消息到队列**
- 原因：无法确定绑卡的最终状态，不应该通知商户

### 4. 绑卡失败场景的回调消息发送

**触发条件**：
- ❌ 绑卡API调用失败
- 错误属于不可重试错误（配置在 `config.yaml` 中）

**实现位置**：
- `updateCardRecordToFailed` 方法中检查不可重试错误
- 调用 `sendFailureCallback` 发送失败回调消息

**不可重试错误示例**：
- "卡号不存在"
- "密码错误" 
- "卡已被其他用户绑定"
- "余额不足"
- "卡片已过期"

## 核心实现方法

### 1. validateAmountAndSendCallback
```go
func (p *BindCardProcessor) validateAmountAndSendCallback(ctx context.Context, msg *BindCardMessage, actualAmount int) error
```
- 使用 `AmountValidatorService` 进行金额验证
- 根据验证结果发送相应的回调消息

### 2. isNonRetryableError
```go
func (p *BindCardProcessor) isNonRetryableError(errorMsg string) bool
```
- 检查错误消息是否属于不可重试错误
- 基于 `config.yaml` 中的 `NonRetryableErrors` 配置

### 3. sendFailureCallback
```go
func (p *BindCardProcessor) sendFailureCallback(ctx context.Context, msg *BindCardMessage, errorMessage string) error
```
- 发送绑卡失败的回调消息
- 包含详细的错误信息

## 回调消息格式

### 成功回调消息
```json
{
  "record_id": "xxx",
  "merchant_id": 123,
  "status": "success",
  "result": {
    "amount": 1000,
    "actual_amount": 1000,
    "validation_passed": true
  },
  "callback_url": "",
  "retry_count": 0,
  "max_retries": 3
}
```

### 失败回调消息（金额不匹配）
```json
{
  "record_id": "xxx", 
  "merchant_id": 123,
  "status": "failed",
  "result": {
    "error_code": "AMOUNT_MISMATCH",
    "error_message": "绑卡金额与真实金额不符",
    "merchant_amount": 1000,
    "actual_amount": 800
  },
  "callback_url": "",
  "retry_count": 0,
  "max_retries": 3
}
```

### 失败回调消息（绑卡失败）
```json
{
  "record_id": "xxx",
  "merchant_id": 123, 
  "status": "failed",
  "result": {
    "error_message": "卡号不存在",
    "error_type": "bind_card_failed"
  },
  "callback_url": "",
  "retry_count": 0,
  "max_retries": 3
}
```

## 队列兼容性

### 队列名称
- Go模块使用：`bind_card_callback_queue`
- Python系统期望：`bind_card_callback_queue`
- ✅ **完全一致**

### 消息格式
- 使用 `queue.CallbackMessage` 结构体
- 与Python系统期望的格式兼容
- 包含必要的字段：`record_id`, `merchant_id`, `status`, `result`

## 关键修改点

### 1. BindCardProcessor结构体
```go
type BindCardProcessor struct {
    // ... 其他字段
    amountValidator *AmountValidatorService  // 新增
}
```

### 2. 构造函数修改
```go
// 初始化金额验证服务
processor.amountValidator = NewAmountValidatorService(db, redis, logrusLogger, config)
```

### 3. 金额更新后的回调处理
```go
// 在 updateCardBalanceRecord 方法末尾添加
if err := p.validateAmountAndSendCallback(ctx, msg, actualAmount); err != nil {
    // 处理回调发送失败
}
```

### 4. 失败场景的回调处理
```go
// 在 updateCardRecordToFailed 方法中添加
if p.isNonRetryableError(err.Error()) {
    if callbackErr := p.sendFailureCallback(ctx, msg, err.Error()); callbackErr != nil {
        // 处理回调发送失败
    }
}
```

## 严格遵循的规则

1. **✅ 绑卡最终成功**：只有API成功 + 金额获取成功 + 金额匹配时才发送成功回调
2. **✅ 金额获取失败**：绑卡成功但金额获取失败时不发送任何回调消息
3. **✅ 绑卡失败**：不可重试错误立即发送失败回调消息
4. **✅ 消息格式兼容**：与Python系统完全兼容
5. **✅ 队列名称一致**：使用相同的队列名称

## 测试建议

1. **成功场景测试**：验证金额匹配时的成功回调
2. **金额不匹配测试**：验证金额不匹配时的失败回调
3. **金额获取失败测试**：验证不发送回调消息
4. **绑卡失败测试**：验证不可重试错误的失败回调
5. **队列消息格式测试**：验证Python系统能正确解析消息

## 日志监控

所有回调消息发送都有详细的日志记录，包括：
- 发送前的条件检查日志
- 发送成功/失败的结果日志
- 错误详情和trace_id用于问题排查
