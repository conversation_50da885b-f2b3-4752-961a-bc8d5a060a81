package test

import (
	"context"
	"testing"
	"time"

	"walmart-bind-card-processor/internal/config"
	"walmart-bind-card-processor/internal/services"
	"walmart-bind-card-processor/internal/queue"

	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// MockQueueManager 模拟队列管理器
type MockQueueManager struct {
	mock.Mock
}

func (m *MockQueueManager) PublishCallback(ctx context.Context, msg *queue.CallbackMessage) error {
	args := m.Called(ctx, msg)
	return args.Error(0)
}

// MockAmountValidator 模拟金额验证服务
type MockAmountValidator struct {
	mock.Mock
}

func (m *MockAmountValidator) ValidateAmount(ctx context.Context, recordID string, requestAmount, actualAmount int) error {
	args := m.Called(ctx, recordID, requestAmount, actualAmount)
	return args.Error(0)
}

// TestCallbackLogic 测试回调逻辑
func TestCallbackLogic(t *testing.T) {
	// 创建模拟对象
	mockQueue := &MockQueueManager{}
	mockValidator := &MockAmountValidator{}
	
	// 创建测试配置
	cfg := &config.Config{
		RetryStrategy: config.RetryStrategyConfig{
			BindCard: config.BindCardRetryConfig{
				NonRetryableErrors: []string{
					"卡号不存在",
					"密码错误",
					"卡已被其他用户绑定",
				},
			},
		},
	}

	// 创建日志器
	logger := zap.NewNop()
	logrusLogger := logrus.New()

	t.Run("金额验证成功应发送成功回调", func(t *testing.T) {
		// 设置模拟期望
		mockValidator.On("ValidateAmount", mock.Anything, "test-record-1", 1000, 1000).Return(nil)
		mockQueue.On("PublishCallback", mock.Anything, mock.MatchedBy(func(msg *queue.CallbackMessage) bool {
			return msg.RecordID == "test-record-1" && msg.Status == "success"
		})).Return(nil)

		// 创建处理器（简化版，只测试回调逻辑）
		processor := &services.BindCardProcessor{}
		
		// 模拟绑卡消息
		msg := &services.BindCardMessage{
			TraceID:    "test-trace-1",
			RecordID:   "test-record-1",
			MerchantID: 123,
			Amount:     1000,
		}

		// 测试金额验证和回调发送
		// 注意：这里需要实际的validateAmountAndSendCallback方法
		// 由于方法依赖于完整的处理器结构，这里只是示例测试结构

		// 验证模拟调用
		mockValidator.AssertExpectations(t)
		mockQueue.AssertExpectations(t)
	})

	t.Run("金额验证失败应发送失败回调", func(t *testing.T) {
		// 设置模拟期望
		mockValidator.On("ValidateAmount", mock.Anything, "test-record-2", 1000, 800).Return(
			assert.AnError, // 模拟金额验证失败
		)
		mockQueue.On("PublishCallback", mock.Anything, mock.MatchedBy(func(msg *queue.CallbackMessage) bool {
			return msg.RecordID == "test-record-2" && msg.Status == "failed"
		})).Return(nil)

		// 模拟绑卡消息
		msg := &services.BindCardMessage{
			TraceID:    "test-trace-2",
			RecordID:   "test-record-2",
			MerchantID: 123,
			Amount:     1000,
		}

		// 测试金额验证失败的回调发送
		// 实际测试需要完整的处理器实例

		// 验证模拟调用
		mockValidator.AssertExpectations(t)
		mockQueue.AssertExpectations(t)
	})

	t.Run("不可重试错误应发送失败回调", func(t *testing.T) {
		// 设置模拟期望
		mockQueue.On("PublishCallback", mock.Anything, mock.MatchedBy(func(msg *queue.CallbackMessage) bool {
			return msg.RecordID == "test-record-3" && msg.Status == "failed"
		})).Return(nil)

		// 测试不可重试错误的检查
		processor := &services.BindCardProcessor{}
		
		// 测试错误消息
		testErrors := []string{
			"卡号不存在",
			"密码错误", 
			"卡已被其他用户绑定",
		}

		for _, errorMsg := range testErrors {
			// 这里需要实际的isNonRetryableError方法测试
			// 由于方法依赖于配置，这里只是示例结构
			t.Logf("测试不可重试错误: %s", errorMsg)
		}

		// 验证模拟调用
		mockQueue.AssertExpectations(t)
	})
}

// TestCallbackMessageFormat 测试回调消息格式
func TestCallbackMessageFormat(t *testing.T) {
	t.Run("成功回调消息格式", func(t *testing.T) {
		msg := &queue.CallbackMessage{
			RecordID:   "test-record",
			MerchantID: 123,
			Status:     "success",
			Result: map[string]interface{}{
				"amount":            1000,
				"actual_amount":     1000,
				"validation_passed": true,
			},
			CallbackURL: "",
			RetryCount:  0,
			MaxRetries:  3,
		}

		assert.Equal(t, "test-record", msg.RecordID)
		assert.Equal(t, uint(123), msg.MerchantID)
		assert.Equal(t, "success", msg.Status)
		assert.Equal(t, 1000, msg.Result["amount"])
		assert.Equal(t, 1000, msg.Result["actual_amount"])
		assert.Equal(t, true, msg.Result["validation_passed"])
	})

	t.Run("失败回调消息格式", func(t *testing.T) {
		msg := &queue.CallbackMessage{
			RecordID:   "test-record",
			MerchantID: 123,
			Status:     "failed",
			Result: map[string]interface{}{
				"error_code":      "AMOUNT_MISMATCH",
				"error_message":   "绑卡金额与真实金额不符",
				"merchant_amount": 1000,
				"actual_amount":   800,
			},
			CallbackURL: "",
			RetryCount:  0,
			MaxRetries:  3,
		}

		assert.Equal(t, "test-record", msg.RecordID)
		assert.Equal(t, uint(123), msg.MerchantID)
		assert.Equal(t, "failed", msg.Status)
		assert.Equal(t, "AMOUNT_MISMATCH", msg.Result["error_code"])
		assert.Equal(t, "绑卡金额与真实金额不符", msg.Result["error_message"])
		assert.Equal(t, 1000, msg.Result["merchant_amount"])
		assert.Equal(t, 800, msg.Result["actual_amount"])
	})
}

// TestNonRetryableErrorDetection 测试不可重试错误检测
func TestNonRetryableErrorDetection(t *testing.T) {
	cfg := &config.Config{
		RetryStrategy: config.RetryStrategyConfig{
			BindCard: config.BindCardRetryConfig{
				NonRetryableErrors: []string{
					"卡号不存在",
					"密码错误",
					"卡已被其他用户绑定",
					"余额不足",
					"卡片已过期",
				},
			},
		},
	}

	testCases := []struct {
		errorMsg    string
		shouldMatch bool
	}{
		{"卡号不存在", true},
		{"密码错误", true},
		{"卡已被其他用户绑定", true},
		{"余额不足", true},
		{"卡片已过期", true},
		{"网络连接超时", false},
		{"服务器繁忙", false},
		{"系统维护中", false},
	}

	for _, tc := range testCases {
		t.Run(tc.errorMsg, func(t *testing.T) {
			// 这里需要实际的isNonRetryableError方法测试
			// 由于方法依赖于完整的处理器，这里只是验证配置
			found := false
			for _, nonRetryableError := range cfg.RetryStrategy.BindCard.NonRetryableErrors {
				if tc.errorMsg == nonRetryableError {
					found = true
					break
				}
			}
			assert.Equal(t, tc.shouldMatch, found, "错误消息 '%s' 的匹配结果不符合预期", tc.errorMsg)
		})
	}
}
