package walmart

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"go.uber.org/zap"
)

// APIClient 沃尔玛API客户端
type APIClient struct {
	encryptionKey string
	version       string
	sign          string
	baseURL       string
	httpClient    *http.Client
	logger        *logrus.Logger
	zapLogger     *zap.Logger
}

// APIConfig API配置
type APIConfig struct {
	EncryptionKey string `json:"encryption_key"`
	Version       string `json:"version"`
	Sign          string `json:"sign"`
	BaseURL       string `json:"base_url"`
}

// BindCardRequest 绑卡请求
type BindCardRequest struct {
	CardNo  string `json:"cardNo"`
	CardPwd string `json:"cardPwd"`
}

// BalanceQueryRequest 余额查询请求
type BalanceQueryRequest struct {
	CardStatus   string `json:"cardStatus"`
	CurrentPage  int    `json:"currentPage"`
	PageSize     int    `json:"pageSize"`
	Sign         string `json:"sign"`
}

// UserInfoRequest 用户信息查询请求
type UserInfoRequest struct {
	CurrentPage int    `json:"currentPage"`
	PageSize    int    `json:"pageSize"`
	Sign        string `json:"sign"`
}

// APIResponse API响应 - 兼容旧格式
type APIResponse struct {
	Success bool                   `json:"success"`
	Code    string                 `json:"code,omitempty"`
	Message string                 `json:"message,omitempty"`
	Data    map[string]interface{} `json:"data,omitempty"`
}

// WalmartAPIResponse 沃尔玛API实际响应格式
type WalmartAPIResponse struct {
	LogID  string                 `json:"logId"`
	Status bool                   `json:"status"`
	Error  *WalmartAPIError       `json:"error,omitempty"`
	Data   map[string]interface{} `json:"data,omitempty"`
}

// WalmartAPIError 沃尔玛API错误信息
type WalmartAPIError struct {
	ErrorCode  int         `json:"errorcode"`
	Message    string      `json:"message"`
	Redirect   interface{} `json:"redirect"`
	Validators interface{} `json:"validators"`
}

// BindCardResult 绑卡结果
type BindCardResult struct {
	Success      bool    `json:"success"`
	Message      string  `json:"message"`
	CardNo       string  `json:"cardNo,omitempty"`
	Balance      string  `json:"balance,omitempty"`
	CardBalance  string  `json:"cardBalance,omitempty"`
	BalanceCnt   string  `json:"balanceCnt,omitempty"`
	LogID        string  `json:"logId,omitempty"`
	ErrorCode    string  `json:"errorCode,omitempty"`
}

// BalanceQueryResult 余额查询结果
type BalanceQueryResult struct {
	Success     bool                     `json:"success"`
	Message     string                   `json:"message"`
	CardList    []map[string]interface{} `json:"cardList,omitempty"`
	TotalCount  int                      `json:"totalCount,omitempty"`
	LogID       string                   `json:"logId,omitempty"`
	ErrorCode   string                   `json:"errorCode,omitempty"`
}

// UserInfoResult 用户信息查询结果
type UserInfoResult struct {
	Success      bool   `json:"success"`
	Message      string `json:"message"`
	NickName     string `json:"nickName,omitempty"`
	CardCount    int    `json:"cardCount,omitempty"`
	TotalBalance string `json:"totalBalance,omitempty"`
	LogID        string `json:"logId,omitempty"`
	ErrorCode    string `json:"errorCode,omitempty"`
}

// NewAPIClient 创建新的API客户端
func NewAPIClient(config APIConfig, logger *logrus.Logger) *APIClient {
	// 创建优化的HTTP传输配置
	transport := &http.Transport{
		MaxIdleConns:        200,              // 最大空闲连接数
		MaxIdleConnsPerHost: 100,              // 每个主机最大空闲连接数
		MaxConnsPerHost:     200,              // 每个主机最大连接数
		IdleConnTimeout:     90 * time.Second, // 空闲连接超时
		TLSHandshakeTimeout: 10 * time.Second, // TLS握手超时
		DisableKeepAlives:   false,            // 启用Keep-Alive
	}

	return &APIClient{
		encryptionKey: config.EncryptionKey,
		version:       config.Version,
		sign:          config.Sign,
		baseURL:       config.BaseURL,
		httpClient: &http.Client{
			Timeout:   30 * time.Second,
			Transport: transport,
		},
		logger: logger,
	}
}

// NewAPIClientWithZap 创建带Zap日志的API客户端
func NewAPIClientWithZap(config APIConfig, logger *logrus.Logger, zapLogger *zap.Logger) *APIClient {
	// 创建优化的HTTP传输配置
	transport := &http.Transport{
		MaxIdleConns:        200,              // 最大空闲连接数
		MaxIdleConnsPerHost: 100,              // 每个主机最大空闲连接数
		MaxConnsPerHost:     200,              // 每个主机最大连接数
		IdleConnTimeout:     90 * time.Second, // 空闲连接超时
		TLSHandshakeTimeout: 10 * time.Second, // TLS握手超时
		DisableKeepAlives:   false,            // 启用Keep-Alive
	}

	return &APIClient{
		encryptionKey: config.EncryptionKey,
		version:       config.Version,
		sign:          config.Sign,
		baseURL:       config.BaseURL,
		httpClient: &http.Client{
			Timeout:   30 * time.Second,
			Transport: transport,
		},
		logger:    logger,
		zapLogger: zapLogger,
	}
}

// ParseCKSign 解析CK签名格式 (sign#encryption_key#version)
func ParseCKSign(ckSign string) (APIConfig, error) {
	parts := strings.Split(ckSign, "#")
	if len(parts) < 3 {
		return APIConfig{}, fmt.Errorf("invalid CK sign format: %s", ckSign)
	}

	return APIConfig{
		Sign:          parts[0],
		EncryptionKey: parts[1],
		Version:       parts[2],
	}, nil
}

// generateNonce 生成随机nonce字符串
func (c *APIClient) generateNonce(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// calculateSignature 计算请求签名
func (c *APIClient) calculateSignature(requestBody map[string]interface{}) (string, error) {
	// 对请求体参数进行排序，确保参数顺序一致
	sortedBody := make(map[string]interface{})
	
	// 获取所有键并排序
	keys := make([]string, 0, len(requestBody))
	for k := range requestBody {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	
	// 按排序后的键重新构建map
	for _, k := range keys {
		sortedBody[k] = requestBody[k]
	}

	// 将排序后的请求体转换为JSON字符串（与Python版本保持一致的格式）
	// Python使用 separators=(",", ":") 确保紧凑格式
	bodyBytes, err := json.Marshal(sortedBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request body: %w", err)
	}

	// 计算HMAC-SHA256签名
	key := []byte(c.encryptionKey)
	h := hmac.New(sha256.New, key)
	h.Write(bodyBytes)
	signature := hex.EncodeToString(h.Sum(nil))
	
	// 转换为大写（与Python实现保持一致）
	return strings.ToUpper(signature), nil
}

// makeRequest 发送API请求
func (c *APIClient) makeRequest(endpoint string, requestBody map[string]interface{}, method string) (*APIResponse, error) {
	// 构建完整URL
	url := c.baseURL
	if !strings.HasSuffix(url, endpoint) {
		if !strings.HasSuffix(url, "/") && !strings.HasPrefix(endpoint, "/") {
			url += "/"
		}
		url += endpoint
	}

	// 生成请求头参数（使用毫秒级时间戳）
	nonce := c.generateNonce(10)
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)

	// 计算签名
	signature, err := c.calculateSignature(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate signature: %w", err)
	}

	// 构建完整的微信小程序请求头（与Python生产环境完全一致）
	headers := map[string]string{
		"sv":               "3",
		"nonce":            nonce,
		"timestamp":        timestamp,
		"signature":        signature,
		"xweb_xhr":         "1",
		"version":          c.version,
		"Sec-Fetch-Site":   "cross-site",
		"Sec-Fetch-Mode":   "cors",
		"Sec-Fetch-Dest":   "empty",
		"Referer":          "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html", // 与Python版本保持一致
		"Accept-Language":  "zh-CN,zh;q=0.9",
		"Content-Type":     "application/json",
		"User-Agent":       "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) XWEB/8555",
	}

	// 序列化请求体
	bodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest(method, url, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// 记录请求日志
	c.logger.WithFields(logrus.Fields{
		"url":       url,
		"method":    method,
		"nonce":     nonce,
		"timestamp": timestamp,
		"signature": signature[:8] + "...", // 只记录签名前8位
	}).Info("Making API request")

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 记录响应日志
	c.logger.WithFields(logrus.Fields{
		"status_code":   resp.StatusCode,
		"response_body": string(respBody),
	}).Info("API response received")

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(respBody, &apiResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &apiResp, nil
}

// BindCard 绑卡API调用 - 完整实现
func (c *APIClient) BindCard(ctx context.Context, cardNo, cardPwd string, debug bool, amount int) (*BindCardResult, error) {
	startTime := time.Now()

	// 记录开始日志
	if c.zapLogger != nil {
		c.zapLogger.Info("开始绑卡API调用",
			zap.String("card_no", maskCardNumber(cardNo)),
			zap.Bool("debug", debug),
		)
	}

	// 检查debug模式
	if debug {
		c.logger.WithFields(logrus.Fields{
			"card_no": maskCardNumber(cardNo),
			"amount": amount,
		}).Info("[TEST_MODE] Calling bind card API (using mock data)")

		return c.createMockBindResult(cardNo, amount), nil
	}

	// 构建绑卡请求参数，严格按照Python生产环境的参数顺序
	requestBody := map[string]interface{}{
		"sign":        c.sign,        // 与Python版本保持一致：第一个参数
		"storeId":     "",            // 与Python版本保持一致：第二个参数
		"userPhone":   "",            // 与Python版本保持一致：第三个参数
		"cardNo":      cardNo,        // 与Python版本保持一致：第四个参数
		"cardPwd":     cardPwd,       // 与Python版本保持一致：第五个参数
		"currentPage": 0,             // 与Python版本保持一致：第六个参数
		"pageSize":    0,             // 与Python版本保持一致：第七个参数
	}

	c.logger.WithFields(logrus.Fields{
		"card_no": maskCardNumber(cardNo),
	}).Info("Calling bind card API")

	// 调用API
	response, err := c.makeRequestWithContext(ctx, "/app/card/mem/bind.json", requestBody, "POST")
	if err != nil {
		// 记录错误日志
		if c.zapLogger != nil {
			c.zapLogger.Error("绑卡API调用失败",
				zap.String("card_no", maskCardNumber(cardNo)),
				zap.Error(err),
				zap.Duration("duration", time.Since(startTime)),
			)
		}
		return &BindCardResult{
			Success:   false,
			Message:   fmt.Sprintf("API调用失败: %v", err),
			ErrorCode: "API_CALL_ERROR",
		}, err
	}

	// 解析响应
	result := c.parseBindCardResponse(response)

	// 记录结果日志
	if c.zapLogger != nil {
		c.zapLogger.Info("绑卡API调用完成",
			zap.String("card_no", maskCardNumber(cardNo)),
			zap.Bool("success", result.Success),
			zap.String("message", result.Message),
			zap.Duration("duration", time.Since(startTime)),
		)
	}

	return result, nil
}

// GetCardBalance 查询卡余额API调用 - 完整实现
func (c *APIClient) GetCardBalance(ctx context.Context, cardNo string, debug bool, amount ...int) (*BalanceQueryResult, error) {
	startTime := time.Now()

	// 记录开始日志
	if c.zapLogger != nil {
		c.zapLogger.Info("开始查询卡余额API调用",
			zap.String("card_no", maskCardNumber(cardNo)),
			zap.Bool("debug", debug),
		)
	}

	// 检查debug模式
	if debug {
		// 获取金额参数，如果没有提供则使用默认值100分
		mockAmount := 100
		if len(amount) > 0 {
			mockAmount = amount[0]
		}

		c.logger.WithFields(logrus.Fields{
			"card_no": maskCardNumber(cardNo),
			"amount": mockAmount,
		}).Info("[TEST_MODE] Calling get card balance API (using mock data)")

		return c.createMockBalanceResult(cardNo, mockAmount), nil
	}

	// 构建金额查询请求参数，严格按照Python版本的参数顺序
	requestBody := map[string]interface{}{
		"cardStatus":  "A",          // 与Python版本保持一致：第一个参数
		"currentPage": 1,            // 与Python版本保持一致：第二个参数
		"pageSize":    10,           // 与Python版本保持一致：第三个参数
		"sign":        c.sign,       // 与Python版本保持一致：第四个参数
	}

	c.logger.WithFields(logrus.Fields{
		"card_no": maskCardNumber(cardNo),
	}).Info("Calling get card balance API")

	// 调用API
	response, err := c.makeRequestWithContext(ctx, "/app/card/mem/pageList.json", requestBody, "POST")
	if err != nil {
		// 记录错误日志
		if c.zapLogger != nil {
			c.zapLogger.Error("查询卡余额API调用失败",
				zap.String("card_no", maskCardNumber(cardNo)),
				zap.Error(err),
				zap.Duration("duration", time.Since(startTime)),
			)
		}
		return &BalanceQueryResult{
			Success:   false,
			Message:   fmt.Sprintf("API调用失败: %v", err),
			ErrorCode: "API_CALL_ERROR",
		}, err
	}

	// 解析响应
	result := c.parseBalanceQueryResponse(response, cardNo)

	// 记录结果日志
	if c.zapLogger != nil {
		c.zapLogger.Info("查询卡余额API调用完成",
			zap.String("card_no", maskCardNumber(cardNo)),
			zap.Bool("success", result.Success),
			zap.String("message", result.Message),
			zap.Int("card_count", len(result.CardList)),
			zap.Duration("duration", time.Since(startTime)),
		)
	}

	return result, nil
}

// QueryUserInfo 查询用户信息API调用 - 完整实现
func (c *APIClient) QueryUserInfo(ctx context.Context, debug bool) (*UserInfoResult, error) {
	startTime := time.Now()

	// 记录开始日志
	if c.zapLogger != nil {
		c.zapLogger.Info("开始查询用户信息API调用",
			zap.Bool("debug", debug),
		)
	}

	// 检查debug模式
	if debug {
		c.logger.Info("[TEST_MODE] Calling query user info API (using mock data)")
		return c.createMockUserInfoResult(), nil
	}

	// 构建用户信息查询请求参数，严格按照生产环境抓包数据的参数顺序
	requestBody := map[string]interface{}{
		"currentPage": 0,
		"pageSize":    0,
		"sign":        c.sign,
	}

	c.logger.Info("Calling query user info API")

	// 调用API
	response, err := c.makeRequestWithContext(ctx, "/app/mem/userInfo.json", requestBody, "POST")
	if err != nil {
		// 记录错误日志
		if c.zapLogger != nil {
			c.zapLogger.Error("查询用户信息API调用失败",
				zap.Error(err),
				zap.Duration("duration", time.Since(startTime)),
			)
		}
		return &UserInfoResult{
			Success:   false,
			Message:   fmt.Sprintf("API调用失败: %v", err),
			ErrorCode: "API_CALL_ERROR",
		}, err
	}

	// 解析响应
	result := c.parseUserInfoResponse(response)

	// 记录结果日志
	if c.zapLogger != nil {
		c.zapLogger.Info("查询用户信息API调用完成",
			zap.Bool("success", result.Success),
			zap.String("message", result.Message),
			zap.String("nick_name", result.NickName),
			zap.Int("card_count", result.CardCount),
			zap.Duration("duration", time.Since(startTime)),
		)
	}

	return result, nil
}

// makeRequestWithContext 带上下文的HTTP请求 - 与Python版本保持一致
func (c *APIClient) makeRequestWithContext(ctx context.Context, endpoint string, requestBody map[string]interface{}, method string) (*APIResponse, error) {
	// 构建完整URL
	url := c.baseURL
	if !strings.HasSuffix(url, endpoint) {
		if !strings.HasSuffix(url, "/") && !strings.HasPrefix(endpoint, "/") {
			url += "/"
		}
		url += endpoint
	}

	// 生成请求头参数（使用毫秒级时间戳）
	nonce := c.generateNonce(10)
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)

	// 计算签名
	signature, err := c.calculateSignature(requestBody)
	if err != nil {
		return nil, fmt.Errorf("计算签名失败: %w", err)
	}

	// 构建完整的微信小程序请求头（与Python生产环境完全一致）
	headers := map[string]string{
		"sv":               "3",
		"nonce":            nonce,
		"timestamp":        timestamp,
		"signature":        signature,
		"xweb_xhr":         "1",
		"version":          c.version,
		"Sec-Fetch-Site":   "cross-site",
		"Sec-Fetch-Mode":   "cors",
		"Sec-Fetch-Dest":   "empty",
		"Referer":          "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html", // 与Python版本保持一致
		"Accept-Language":  "zh-CN,zh;q=0.9",
		"Content-Type":     "application/json",
		"User-Agent":       "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) XWEB/8555",
	}

	// 序列化请求体（直接使用原始请求体，与Python版本保持一致）
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 记录原始响应用于调试
	if c.zapLogger != nil {
		c.zapLogger.Debug("API原始响应", zap.String("body", string(body)))
	}

	// 尝试解析为新的沃尔玛API响应格式
	var walmartResp WalmartAPIResponse
	if err := json.Unmarshal(body, &walmartResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 转换为兼容的APIResponse格式
	apiResp := &APIResponse{
		Success: walmartResp.Status,
		Data:    walmartResp.Data,
	}

	// 设置LogID到Data中
	if apiResp.Data == nil {
		apiResp.Data = make(map[string]interface{})
	}
	apiResp.Data["logId"] = walmartResp.LogID

	// 处理错误信息
	if walmartResp.Error != nil {
		apiResp.Success = false
		apiResp.Code = fmt.Sprintf("%d", walmartResp.Error.ErrorCode)
		apiResp.Message = walmartResp.Error.Message

		// 将错误信息也放入Data中，保持与现有解析逻辑兼容
		apiResp.Data["error"] = map[string]interface{}{
			"errorcode": walmartResp.Error.ErrorCode,
			"message":   walmartResp.Error.Message,
			"redirect":  walmartResp.Error.Redirect,
			"validators": walmartResp.Error.Validators,
		}
	} else if !walmartResp.Status {
		// 如果status为false但没有error字段，设置默认错误
		apiResp.Success = false
		apiResp.Message = "操作失败"
	}

	return apiResp, nil
}

// setRequestHeaders 设置请求头
func (c *APIClient) setRequestHeaders(req *http.Request) {
	// 设置基础请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.20(0x18001428) NetType/WIFI Language/zh_CN")

	// 微信小程序相关头部
	req.Header.Set("Referer", "https://servicewechat.com/wx91d27dbf599dff74/673/page-frame.html")
	req.Header.Set("Accept-Language", "zh-cn")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br")
	req.Header.Set("Connection", "keep-alive")
}

// parseBindCardResponse 解析绑卡响应
func (c *APIClient) parseBindCardResponse(response *APIResponse) *BindCardResult {
	result := &BindCardResult{
		Success: response.Success,
		Message: response.Message,
	}

	// 设置LogID（从顶层获取）
	if response.Data != nil {
		if logID, ok := response.Data["logId"].(string); ok {
			result.LogID = logID
		}
	}

	if response.Success {
		// 绑卡成功：真实API的data字段通常是数字1，不包含卡片信息
		// 卡片信息需要通过单独的获取卡包接口获取
		if response.Data != nil {
			if cardNo, ok := response.Data["cardNo"].(string); ok {
				result.CardNo = cardNo
			}
			// 注意：真实绑卡接口不返回余额信息
			if balance, ok := response.Data["balance"].(string); ok {
				result.Balance = balance
			}
			if cardBalance, ok := response.Data["cardBalance"].(string); ok {
				result.CardBalance = cardBalance
			}
			if balanceCnt, ok := response.Data["balanceCnt"].(string); ok {
				result.BalanceCnt = balanceCnt
			}
		}
	} else {
		// 绑卡失败：解析error字段中的错误信息
		// 真实格式：{"error":{"errorcode":10131,"message":"该电子卡已被其他用户绑定"}}
		if errorData, ok := response.Data["error"].(map[string]interface{}); ok {
			if errorCode, ok := errorData["errorcode"].(float64); ok {
				result.ErrorCode = fmt.Sprintf("%.0f", errorCode)
			}
			if errorMessage, ok := errorData["message"].(string); ok {
				result.Message = errorMessage
			}
		}

		// 如果没有从error字段获取到错误码，使用response.Code
		if result.ErrorCode == "" {
			result.ErrorCode = response.Code
			if result.ErrorCode == "" {
				result.ErrorCode = "UNKNOWN_ERROR"
			}
		}
	}

	return result
}

// createMockBindResult 创建模拟绑卡结果
func (c *APIClient) createMockBindResult(cardNo string, amount int) *BindCardResult {
	// 测试模式下100%返回成功
	return &BindCardResult{
		Success:     true,
		Message:     "",    // 成功时message为null/空
		CardNo:      cardNo,
		Balance:     "",    // 绑卡接口不返回金额，留空
		CardBalance: "",    // 绑卡接口不返回金额，留空
		BalanceCnt:  "",    // 绑卡接口不返回金额，留空
		LogID:       fmt.Sprintf("TEST_MOCK_%d", time.Now().UnixNano()),
	}
}

// createMockBindResponse 创建模拟绑卡响应
func (c *APIClient) createMockBindResponse(cardNo string) *APIResponse {
	return &APIResponse{
		Success: true,
		Code:    "200",
		Message: "Mock bind success",
		Data: map[string]interface{}{
			"cardNo":      cardNo,
			"balance":     "10000",
			"cardBalance": "100.00",
			"balanceCnt":  "100.00",
			"logId":       fmt.Sprintf("MOCK_BIND_%d", time.Now().Unix()),
			"status":      true,
		},
	}
}

// createMockBalanceResponse 创建模拟余额查询响应
func (c *APIClient) createMockBalanceResponse(cardNo string) *APIResponse {
	return &APIResponse{
		Success: true,
		Code:    "200",
		Message: "Mock balance query success",
		Data: map[string]interface{}{
			"cardList": []map[string]interface{}{
				{
					"cardNo":      cardNo,
					"balance":     "10000",
					"cardBalance": "100.00",
					"balanceCnt":  "100.00",
					"cardStatus":  "A",
				},
			},
			"totalCount": 1,
			"logId":      fmt.Sprintf("MOCK_BALANCE_%d", time.Now().Unix()),
			"status":     true,
		},
	}
}

// createMockUserInfoResponse 创建模拟用户信息响应
func (c *APIClient) createMockUserInfoResponse() *APIResponse {
	return &APIResponse{
		Success: true,
		Code:    "200",
		Message: "Mock user info query success",
		Data: map[string]interface{}{
			"nickName":   "测试用户",
			"cardCount":  5,
			"totalBalance": "50000",
			"logId":      fmt.Sprintf("MOCK_USER_%d", time.Now().Unix()),
			"status":     true,
		},
	}
}

// maskCardNumber 遮蔽卡号敏感信息
func maskCardNumber(cardNo string) string {
	if len(cardNo) <= 6 {
		return cardNo
	}
	return cardNo[:6] + "***"
}

// SetBaseURL 设置基础URL
func (c *APIClient) SetBaseURL(baseURL string) {
	c.baseURL = baseURL
}

// GetBaseURL 获取基础URL
func (c *APIClient) GetBaseURL() string {
	return c.baseURL
}

// parseBalanceQueryResponse 解析余额查询响应
func (c *APIClient) parseBalanceQueryResponse(response *APIResponse, cardNo string) *BalanceQueryResult {
	result := &BalanceQueryResult{
		Success: response.Success,
		Message: response.Message,
	}

	if response.Data != nil {
		// 解析卡片列表
		if cardListData, ok := response.Data["cardList"].([]interface{}); ok {
			cardList := make([]map[string]interface{}, 0, len(cardListData))
			for _, cardData := range cardListData {
				if cardMap, ok := cardData.(map[string]interface{}); ok {
					cardList = append(cardList, cardMap)
				}
			}
			result.CardList = cardList
		}

		// 解析总数
		if totalCount, ok := response.Data["totalCount"].(float64); ok {
			result.TotalCount = int(totalCount)
		}

		// 解析日志ID
		if logID, ok := response.Data["logId"].(string); ok {
			result.LogID = logID
		}
	}

	// 设置错误码
	if !response.Success {
		result.ErrorCode = response.Code
		if result.ErrorCode == "" {
			result.ErrorCode = "UNKNOWN_ERROR"
		}
	}

	return result
}

// parseUserInfoResponse 解析用户信息响应
func (c *APIClient) parseUserInfoResponse(response *APIResponse) *UserInfoResult {
	result := &UserInfoResult{
		Success: response.Success,
		Message: response.Message,
	}

	if response.Data != nil {
		// 解析昵称
		if nickName, ok := response.Data["nickName"].(string); ok {
			result.NickName = nickName
		}

		// 解析卡片数量
		if cardCount, ok := response.Data["cardCount"].(float64); ok {
			result.CardCount = int(cardCount)
		}

		// 解析总余额
		if totalBalance, ok := response.Data["totalBalance"].(string); ok {
			result.TotalBalance = totalBalance
		}

		// 解析日志ID
		if logID, ok := response.Data["logId"].(string); ok {
			result.LogID = logID
		}
	}

	// 设置错误码
	if !response.Success {
		result.ErrorCode = response.Code
		if result.ErrorCode == "" {
			result.ErrorCode = "UNKNOWN_ERROR"
		}
	}

	return result
}

// createMockBalanceResult 创建模拟余额查询结果
func (c *APIClient) createMockBalanceResult(cardNo string, amount int) *BalanceQueryResult {
	// 测试模式下100%返回成功
	// 将金额从分转换为元（保留两位小数）
	amountInYuan := fmt.Sprintf("%.2f", float64(amount)/100.0)

	// 模拟真实获取卡包接口的返回格式
	return &BalanceQueryResult{
		Success: true,
		Message: "",    // 成功时message为null/空
		CardList: []map[string]interface{}{
			{
				"id":                   167305744,
				"mchNo":               "100530000004",
				"cardNo":              cardNo,
				"code":                "184700372",
				"nickName":            "微信用户（测试模式）",
				"userId":              30045432,
				"sourceType":          "绑卡",
				"cardId":              1,
				"wxCardId":            "ppN5bt0i6b39LoRV0kggnrHaf4Gk",
				"giveId":              0,
				"receiveStatus":       false,
				"status":              1,
				"type":                1,
				"createUserId":        30045432,
				"storeId":             "",
				"storeName":           nil,
				"channelType":         1,
				"holdCardTime":        time.Now().UnixMilli(),
				"colorType":           0,
				"bgPic":               4462,
				"bgPicUrl":            "http://mmbiz.qpic.cn/mmbiz_jpg/MjbyiaBuoxn5Q6fFaDtK0lb0ZkEGBTHoLM6yWU1w2Be5WViaXia5ZvsO7aKtUPZkulsSLb1dflZ3ibaMcicBqE4kxOA/0",
				"initMoney":           amount,                      // 使用用户请求的金额（分）
				"lastHoldCardTime":    time.Now().UnixMilli(),
				"updateTime":          time.Now().UnixMilli(),
				"createTime":          time.Now().UnixMilli(),
				"giveFlag":            true,
				"transferFlag":        true,
				"channelId":           0,
				"channelName":         nil,
				"lockFlag":            false,
				"cardBalance":         amount,                      // 单位：分，使用用户请求的原始金额
				"upcardCardStatus":    nil,
				"cardExpiryDate":      nil,
				"upcardType":          0,
				"bizType":             1,
				"startTime":           nil,
				"endTime":             nil,
				"totalUserNum":        nil,
				"totalCardNum":        nil,
				"balance":             amountInYuan,               // 单位：元，根据实际金额计算
				"expiredDate":         "2028.06.30",
				"brandName":           nil,
				"wxLogoUrl":           nil,
				"logo":                nil,
				"cardName":            nil,
				"statusList":          nil,
				"flag":                nil,
				"sign":                nil,
				"cardColor":           nil,
				"qrcode":              nil,
				"expires":             0,
				"url":                 nil,
				"walWxMemberCardDTO":  nil,
				"unbindType":          nil,
				"gloryCdKey":          nil,
				"gloryExpireDate":     nil,
				"allowUnbind":         1,
				"allowExpire":         0,
				"cardExpiredDate":     "20280630",
				"expireMsg":           nil,
				"hotReason":           nil,
				"consumeFlag":         nil,
				"consumeLimitInfo":    nil,
				"bizTypeList":         nil,
				"bgPicEnc":            "4462?sign=4C04373E819BDC3B641A155709612740",
				"historyFlag":         nil,
				"tbIndex":             nil,
				"tbIndex2":            nil,
				"userTotalFlag":       nil,
				"giveCheck":           nil,
				"expireDateCnt":       "2028.06.30",
				"expireDateInt":       "2028.06.30",
				"expireDateCount":     365,
				"balanceCnt":          amountInYuan,               // 单位：元，根据实际金额计算
			},
		},
		TotalCount: 1,
		LogID:      fmt.Sprintf("TEST_MOCK_%d", time.Now().UnixNano()),
	}
}

// createMockUserInfoResult 创建模拟用户信息结果
func (c *APIClient) createMockUserInfoResult() *UserInfoResult {
	// 测试模式下100%返回成功
	return &UserInfoResult{
		Success:      true,
		Message:      "",    // 成功时message为null/空
		NickName:     "微信用户（测试模式）",
		CardCount:    4,
		TotalBalance: "",    // 用户信息接口不返回总余额
		LogID:        fmt.Sprintf("TEST_MOCK_%d", time.Now().UnixNano()),
	}
}
