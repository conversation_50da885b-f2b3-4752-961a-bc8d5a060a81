package main

import (
	"fmt"
	"strings"
	"testing"

	"walmart-bind-card-processor/internal/config"

	"github.com/stretchr/testify/assert"
)

// TestNonRetryableErrorDetection 测试不可重试错误检测
func TestNonRetryableErrorDetection(t *testing.T) {
	// 加载配置
	cfg, err := config.LoadConfig("../config.yaml")
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	// 打印配置中的不可重试错误列表
	fmt.Println("配置中的不可重试错误列表:")
	for i, errorMsg := range cfg.RetryStrategy.BindCard.NonRetryableErrors {
		fmt.Printf("%d. %s\n", i+1, errorMsg)
	}

	// 测试用例
	testCases := []struct {
		errorMsg    string
		shouldMatch bool
	}{
		{"商户没有可用CK", true},
		{"商户没有可用CK: 商户没有可用CK", true},
		{"CK切换失败", true},
		{"CK切换失败：尝试3次后仍无法获取可用CK", true},
		{"卡号不存在", true},
		{"密码错误", true},
		{"网络连接超时", false},
		{"服务器繁忙", false},
	}

	for _, tc := range testCases {
		t.Run(tc.errorMsg, func(t *testing.T) {
			// 模拟 isNonRetryableError 方法的逻辑
			found := false
			for _, nonRetryableError := range cfg.RetryStrategy.BindCard.NonRetryableErrors {
				if strings.Contains(tc.errorMsg, nonRetryableError) {
					found = true
					break
				}
			}

			assert.Equal(t, tc.shouldMatch, found, 
				"错误消息 '%s' 的匹配结果不符合预期", tc.errorMsg)
			
			if found {
				fmt.Printf("✅ '%s' 被识别为不可重试错误\n", tc.errorMsg)
			} else {
				fmt.Printf("❌ '%s' 未被识别为不可重试错误\n", tc.errorMsg)
			}
		})
	}
}

// TestConfigLoading 测试配置加载
func TestConfigLoading(t *testing.T) {
	cfg, err := config.LoadConfig("../config.yaml")
	assert.NoError(t, err, "配置加载应该成功")

	// 验证绑卡重试配置
	bindCardConfig := cfg.RetryStrategy.BindCard
	assert.NotEmpty(t, bindCardConfig.NonRetryableErrors, "不可重试错误列表不应为空")

	// 检查关键错误是否存在
	nonRetryableErrors := bindCardConfig.NonRetryableErrors
	
	// 检查是否包含我们添加的错误
	containsMerchantNoCK := false
	containsCKSwitchFailed := false
	
	for _, errorMsg := range nonRetryableErrors {
		if strings.Contains("商户没有可用CK", errorMsg) {
			containsMerchantNoCK = true
		}
		if strings.Contains("CK切换失败", errorMsg) {
			containsCKSwitchFailed = true
		}
	}

	assert.True(t, containsMerchantNoCK, "配置应包含'商户没有可用CK'错误")
	assert.True(t, containsCKSwitchFailed, "配置应包含'CK切换失败'错误")

	fmt.Printf("配置验证结果:\n")
	fmt.Printf("  不可重试错误总数: %d\n", len(nonRetryableErrors))
	fmt.Printf("  包含'商户没有可用CK': %v\n", containsMerchantNoCK)
	fmt.Printf("  包含'CK切换失败': %v\n", containsCKSwitchFailed)
}
