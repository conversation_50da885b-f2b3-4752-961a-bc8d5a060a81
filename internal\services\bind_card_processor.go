package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"walmart-bind-card-processor/internal/config"
	"walmart-bind-card-processor/internal/model"
	"walmart-bind-card-processor/internal/queue"
	"walmart-bind-card-processor/internal/utils"
	"walmart-bind-card-processor/pkg/walmart"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	amqp "github.com/rabbitmq/amqp091-go"
	"github.com/sirupsen/logrus"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// maskCardNumber 遮蔽卡号敏感信息
func maskCardNumber(cardNo string) string {
	if len(cardNo) <= 8 {
		return cardNo
	}
	return cardNo[:4] + "****" + cardNo[len(cardNo)-4:]
}

// BindCardMessage 绑卡消息
type BindCardMessage struct {
	TraceID      string    `json:"trace_id"`
	RecordID     string    `json:"record_id"`
	MerchantID   uint      `json:"merchant_id"`
	DepartmentID *uint     `json:"department_id,omitempty"`
	CardNumber   string    `json:"card_number"`
	CardPassword string    `json:"card_password"`
	Amount       int       `json:"amount"`
	RetryCount   int       `json:"retry_count"`
	MaxRetries   int       `json:"max_retries"`
	CreatedAt    time.Time `json:"created_at"`
	Debug        bool      `json:"debug"`
}

// BindCardProcessor 绑卡处理器
type BindCardProcessor struct {
	db                    *gorm.DB
	redis                 *redis.Client
	logger                *zap.Logger
	config                *config.Config

	// 核心服务
	weightManager         *CKWeightManager
	preoccupationManager  *CKPreoccupationManager
	retryStrategy         *RetryStrategyService
	statusSyncService     *CKStatusSyncService
	monitoringService     *CKMonitoringService
	amountValidator       *AmountValidatorService

	// 消息队列 - 使用统一的队列管理器
	queueManager          *queue.Manager

	// 协程池管理器
	goroutinePool         *utils.GoroutinePool

	// 消息去重
	deduplicationTTL      time.Duration


}

// BindCardTask 绑卡处理任务，实现Task接口
type BindCardTask struct {
	Message   *BindCardMessage
	Processor *BindCardProcessor
	Context   context.Context
}

// Execute 执行绑卡任务
func (t *BindCardTask) Execute(ctx context.Context) error {
	return t.Processor.ProcessBindCard(ctx, t.Message)
}

// GetPriority 获取任务优先级
func (t *BindCardTask) GetPriority() int {
	// 默认优先级为0，可以根据业务需求调整
	return 0
}

// GetID 获取任务ID
func (t *BindCardTask) GetID() string {
	return t.Message.TraceID
}

// NewBindCardProcessor 创建绑卡处理器
func NewBindCardProcessor(
	db *gorm.DB,
	redis *redis.Client,
	logger *zap.Logger,
	config *config.Config,
	queueManager *queue.Manager,
) (*BindCardProcessor, error) {

	// 创建服务工厂
	serviceFactory := NewServiceFactory(config, db, redis, logger)

	// 创建协程池配置 - 使用配置文件中的高并发设置
	poolConfig := utils.GoroutinePoolConfig{
		CoreSize:     config.Concurrency.GoroutinePool.CoreSize,     // 从配置读取：500
		MaxSize:      config.Concurrency.GoroutinePool.MaxSize,      // 从配置读取：1000
		KeepAlive:    config.Concurrency.GoroutinePool.KeepAlive,    // 从配置读取
		QueueSize:    config.Concurrency.GoroutinePool.QueueCapacity, // 从配置读取：2000
		RejectPolicy: "caller_runs",                                  // 拒绝策略：调用者运行
	}

	// 创建协程池
	goroutinePool := utils.NewGoroutinePool(poolConfig, logger)

	processor := &BindCardProcessor{
		db:                   db,
		redis:                redis,
		logger:               logger,
		config:               config,
		queueManager:         queueManager,
		goroutinePool:        goroutinePool,
		deduplicationTTL:     10 * time.Minute,
		weightManager:        serviceFactory.CreateCKWeightManager(),
		preoccupationManager: serviceFactory.CreateCKPreoccupationManager(),
		statusSyncService:    serviceFactory.CreateCKStatusSyncService(),
		monitoringService:    serviceFactory.CreateCKMonitoringService(),
		amountValidator:      nil, // 将在下面初始化
	}

	// 初始化重试策略服务（创建logrus日志器）
	logrusLogger := logrus.New()
	logrusLogger.SetLevel(logrus.InfoLevel)
	ckManager := NewCKManagerService(db, logrusLogger)
	processor.retryStrategy = NewRetryStrategyService(config, logrusLogger, ckManager)

	// 初始化金额验证服务
	processor.amountValidator = NewAmountValidatorService(db, redis, logrusLogger, config)

	logger.Info("绑卡处理器创建成功，等待启动时初始化队列")

	return processor, nil
}

// createAPIClientFromCK 根据CK记录动态创建API客户端
func (p *BindCardProcessor) createAPIClientFromCK(ckSign string, ckID uint) (*walmart.APIClient, error) {
	// 解析CK签名获取API参数
	apiConfig, err := walmart.ParseCKSign(ckSign)
	if err != nil {
		return nil, fmt.Errorf("解析CK签名失败: %w", err)
	}

	// API地址写死在代码中保护机密信息
	apiConfig.BaseURL = "https://apicard.swiftpass.cn"

	p.logger.Info("创建API客户端",
		zap.Uint("ck_id", ckID),
		zap.String("base_url", apiConfig.BaseURL))

	// 创建logrus日志器（API客户端需要）
	logrusLogger := logrus.New()
	logrusLogger.SetLevel(logrus.InfoLevel)

	// 创建API客户端
	return walmart.NewAPIClientWithZap(apiConfig, logrusLogger, p.logger), nil
}


// Start 启动绑卡处理器
func (p *BindCardProcessor) Start(ctx context.Context) error {
	p.logger.Info("启动绑卡处理器")

	// 初始化队列（延迟到启动时）
	if err := p.initializeQueues(); err != nil {
		return fmt.Errorf("初始化队列失败: %w", err)
	}
	p.logger.Info("队列初始化完成")

	// 启动消息消费者
	go p.startBindCardConsumer(ctx)
	// 暂时禁用重试消费者，避免创建临时队列
	// go p.startRetryConsumer(ctx)

	p.logger.Info("绑卡处理器启动完成，开始处理消息")

	// 等待上下文取消
	<-ctx.Done()

	p.logger.Info("绑卡处理器停止")
	return nil
}

// Stop 停止绑卡处理器
func (p *BindCardProcessor) Stop(ctx context.Context) error {
	p.logger.Info("停止绑卡处理器")

	// 关闭协程池
	if p.goroutinePool != nil {
		p.goroutinePool.Shutdown(30 * time.Second) // 30秒优雅关闭超时
		p.logger.Info("协程池已关闭")
	}

	// 队列管理器的关闭由服务容器统一管理
	// 这里不需要手动关闭连接

	return nil
}

// ProcessBindCard 处理绑卡消息
func (p *BindCardProcessor) ProcessBindCard(ctx context.Context, msg *BindCardMessage) error {
	processStartTime := time.Now()

	p.logger.Info("开始处理绑卡消息",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.Uint("merchant_id", msg.MerchantID),
		zap.Int("retry_count", msg.RetryCount),
		zap.Time("start_time", processStartTime))

	// 步骤1: 记录收到绑卡数据
	if err := p.recordSystemLog(ctx, msg, "从队列接收到绑卡任务", map[string]interface{}{
		"trace_id":            msg.TraceID,
		"client_ip":           "127.0.0.1", // TODO: 从上下文获取真实IP
		"is_recovery":         msg.RetryCount > 0,
		"merchant_id":         msg.MerchantID,
		"has_ext_data":        false, // TODO: 根据实际情况设置
		"recovery_attempt":    nil,
		"has_card_password":   msg.CardPassword != "",
		"queue_received_at":   processStartTime.Format(time.RFC3339),
	}); err != nil {
		p.logger.Error("记录队列接收日志失败", zap.Error(err))
	}

	// 步骤2: 记录开始处理
	if err := p.recordSystemLog(ctx, msg, "开始处理绑卡请求", map[string]interface{}{
		"ext_data":            nil, // TODO: 根据实际情况设置
		"merchant_id":         msg.MerchantID,
		"process_start_time":  processStartTime.Format(time.RFC3339),
	}); err != nil {
		p.logger.Error("记录开始处理日志失败", zap.Error(err))
	}
	
	// 消息去重检查 - 基于数据库记录状态而不是Redis标记
	if isDuplicate, err := p.checkMessageDuplicationByDBStatus(ctx, msg); err != nil {
		return fmt.Errorf("检查消息去重失败: %w", err)
	} else if isDuplicate {
		p.logger.Info("重复消息，跳过处理",
			zap.String("trace_id", msg.TraceID),
			zap.String("record_id", msg.RecordID))
		return nil
	}
	
	// 选择部门（如果未指定）
	deptSelectionStart := time.Now()
	departmentID := msg.DepartmentID
	if departmentID == nil {
		dept, err := p.weightManager.SelectDepartmentByWeight(ctx, msg.MerchantID)
		if err != nil {
			return p.handleBindCardError(ctx, msg, fmt.Errorf("商户没有可用CK: %w", err))
		}
		departmentID = &dept.ID
	}
	deptSelectionEnd := time.Now()

	// 记录部门选择完成日志
	if err := p.recordBindingLogWithDepartment(ctx, msg, "DEPT_SELECTION", "completed",
		fmt.Sprintf("部门选择完成，选中部门ID: %d", *departmentID),
		deptSelectionStart, deptSelectionEnd, map[string]interface{}{
			"department_id": *departmentID,
			"selection_time_ms": deptSelectionEnd.Sub(deptSelectionStart).Milliseconds(),
		}, departmentID); err != nil {
		p.logger.Error("记录部门选择日志失败", zap.Error(err))
	}
	
	// 预占用CK
	preoccupyStart := time.Now()
	preoccupyReq := &CKPreoccupationRequest{
		MerchantID:   msg.MerchantID,
		DepartmentID: *departmentID,
		TraceID:      msg.TraceID,
		RecordID:     msg.RecordID,
		Amount:       msg.Amount,
		CardNumber:   msg.CardNumber,
	}

	record, err := p.preoccupationManager.PreoccupyCK(ctx, preoccupyReq)
	preoccupyEnd := time.Now()

	if err != nil {
		// 记录预占用失败日志
		if logErr := p.recordBindingLogWithDepartment(ctx, msg, "CK_PREOCCUPATION", "failed",
			fmt.Sprintf("CK预占用失败: %s", err.Error()),
			preoccupyStart, preoccupyEnd, map[string]interface{}{
				"error": err.Error(),
				"department_id": *departmentID,
			}, departmentID); logErr != nil {
			p.logger.Error("记录预占用失败日志失败", zap.Error(logErr))
		}
		return p.handleBindCardError(ctx, msg, fmt.Errorf("预占用CK失败: %w", err))
	}

	// 记录预占用成功日志
	if err := p.recordBindingLogWithDepartment(ctx, msg, "CK_PREOCCUPATION", "completed",
		fmt.Sprintf("CK预占用成功，CK_ID: %d", record.CKID),
		preoccupyStart, preoccupyEnd, map[string]interface{}{
			"ck_id": record.CKID,
			"department_id": *departmentID,
			"preoccupation_time_ms": preoccupyEnd.Sub(preoccupyStart).Milliseconds(),
		}, departmentID); err != nil {
		p.logger.Error("记录预占用成功日志失败", zap.Error(err))
	}
	
	// 步骤3: 执行绑卡API调用
	bindStart := time.Now()

	// 记录沃尔玛API请求日志
	requestData := map[string]interface{}{
		"cardNo":  msg.CardNumber,
		"cardPwd": "******", // 密码脱敏
	}
	if err := p.recordWalmartRequestLogWithDepartment(ctx, msg, 1, record.CKID, requestData, departmentID); err != nil {
		p.logger.Error("记录沃尔玛API请求日志失败", zap.Error(err))
	}

	bindResult, err := p.executeBindCard(ctx, msg, record.CKID)
	bindEnd := time.Now()
	bindDuration := float64(bindEnd.Sub(bindStart).Milliseconds())

	// 记录沃尔玛API响应日志
	if err := p.recordWalmartResponseLogWithDepartment(ctx, msg, 1, record.CKID, bindResult, bindDuration, departmentID); err != nil {
		p.logger.Error("记录沃尔玛API响应日志失败", zap.Error(err))
	}

	// 记录绑卡尝试结果日志
	success := err == nil && bindResult != nil && bindResult.Success
	if err := p.recordBindAttemptLogWithDepartment(ctx, msg, 1, record.CKID, success, bindDuration, bindResult, departmentID); err != nil {
		p.logger.Error("记录绑卡尝试结果日志失败", zap.Error(err))
	}

	if err != nil {
		// 回滚预占用
		if commitErr := p.preoccupationManager.CommitPreoccupation(ctx, msg.TraceID, msg.RecordID, false, 0); commitErr != nil {
			p.logger.Error("回滚预占用失败", zap.Error(commitErr))
		}

		// 检查是否需要CK故障切换
		if p.shouldSwitchCK(bindResult) {
			return p.handleCKFailureAndSwitch(ctx, msg, record.CKID, err)
		}

		return p.handleBindCardError(ctx, msg, fmt.Errorf("执行绑卡失败: %w", err))
	}

	// 步骤4: 检查绑卡结果，如果失败且需要切换CK，则进行切换
	if !bindResult.Success && p.shouldSwitchCK(bindResult) {
		p.logger.Warn("绑卡失败且需要切换CK",
			zap.String("trace_id", msg.TraceID),
			zap.Uint("failed_ck_id", record.CKID),
			zap.String("error_message", bindResult.Message))

		// 🔧 修复：先保存当前绑卡结果和使用的CK ID，确保故障CK被正确记录
		if err := p.updateBindCardRecordStatusWithoutProcessTime(ctx, msg, bindResult, record.CKID, *departmentID); err != nil {
			p.logger.Error("保存故障CK记录失败",
				zap.String("trace_id", msg.TraceID),
				zap.Uint("failed_ck_id", record.CKID),
				zap.Error(err))
			// 继续执行CK切换，不因为记录保存失败而中断
		} else {
			p.logger.Info("已保存故障CK记录",
				zap.String("trace_id", msg.TraceID),
				zap.Uint("failed_ck_id", record.CKID),
				zap.String("error_message", bindResult.Message))
		}

		// 回滚预占用
		if commitErr := p.preoccupationManager.CommitPreoccupation(ctx, msg.TraceID, msg.RecordID, false, 0); commitErr != nil {
			p.logger.Error("回滚预占用失败", zap.Error(commitErr))
		}

		// 进行CK切换
		return p.handleCKFailureAndSwitch(ctx, msg, record.CKID, fmt.Errorf("绑卡失败: %s", bindResult.Message))
	}

	// 步骤5: 确认预占用
	if err := p.preoccupationManager.CommitPreoccupation(ctx, msg.TraceID, msg.RecordID, bindResult.Success, bindResult.ActualAmount); err != nil {
		p.logger.Error("确认预占用失败", zap.Error(err))
	}

	// 🔧 修复：先更新基本状态（不包含最终process_time）
	if err := p.updateBindCardRecordStatusWithoutProcessTime(ctx, msg, bindResult, record.CKID, *departmentID); err != nil {
		p.logger.Error("更新绑卡记录状态失败",
			zap.String("trace_id", msg.TraceID),
			zap.String("record_id", msg.RecordID),
			zap.Error(err))
		// 注意：这里不返回错误，因为绑卡操作本身已经完成
	}

	// 如果绑卡成功，获取卡片真实金额（在状态更新之后，避免被覆盖）
	if bindResult.Success {
		if err := p.fetchCardBalance(ctx, msg, record.CKID); err != nil {
			p.logger.Error("获取卡片金额失败", zap.Error(err))
			// 金额获取失败不影响绑卡成功状态
		}

		// 绑卡成功后标记消息已处理（防重复）
		if err := p.markMessageProcessed(ctx, msg); err != nil {
			p.logger.Error("标记消息已处理失败", zap.Error(err))
		}
	}

	// 🔧 修复：在所有处理完成后，最后更新准确的process_time
	if err := p.updateFinalProcessTime(ctx, msg); err != nil {
		p.logger.Error("更新最终处理时间失败", zap.Error(err))
	}

	// 记录处理结果
	endTime := time.Now()
	totalDuration := endTime.Sub(processStartTime)

	// 步骤5: 记录绑卡完成（不再使用旧的日志方法，所有详细步骤已在上面记录）

	p.logger.Info("绑卡消息处理完成",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.Uint("ck_id", record.CKID),
		zap.Uint("department_id", *departmentID),
		zap.Bool("success", bindResult.Success),
		zap.Duration("duration", totalDuration),
		zap.Time("start_time", processStartTime),
		zap.Time("end_time", endTime))

	return nil
}

// executeBindCard 执行绑卡操作
func (p *BindCardProcessor) executeBindCard(ctx context.Context, msg *BindCardMessage, ckID uint) (*BindCardResult, error) {
	// 开始监控记录
	operationLog := p.monitoringService.StartOperation(ckID, "bind_card", msg.TraceID, msg.RecordID)
	operationLog.MerchantID = msg.MerchantID
	operationLog.CardNumber = maskCardNumber(msg.CardNumber)
	operationLog.Amount = msg.Amount

	p.logger.Info("执行绑卡操作",
		zap.String("trace_id", msg.TraceID),
		zap.Uint("ck_id", ckID),
		zap.String("card_number", maskCardNumber(msg.CardNumber)))

	// 获取CK信息
	var ck struct {
		ID   uint   `gorm:"column:id"`
		Sign string `gorm:"column:sign"`
	}

	if err := p.db.Table("walmart_ck").Where("id = ?", ckID).First(&ck).Error; err != nil {
		result := &BindCardResult{
			TraceID:      msg.TraceID,
			RecordID:     msg.RecordID,
			CKID:         ckID,
			Success:      false,
			Message:      fmt.Sprintf("获取CK信息失败: %v", err),
			ActualAmount: 0,
		}

		// 记录操作失败
		p.monitoringService.FinishOperation(operationLog, false, err.Error(), "CK_NOT_FOUND")

		return result, err
	}

	// 动态创建API客户端
	apiClient, err := p.createAPIClientFromCK(ck.Sign, ck.ID)
	if err != nil {
		return &BindCardResult{
			TraceID:      msg.TraceID,
			RecordID:     msg.RecordID,
			CKID:         ckID,
			Success:      false,
			Message:      fmt.Sprintf("创建API客户端失败: %v", err),
			ActualAmount: 0,
		}, err
	}

	// 调用真实的沃尔玛绑卡API
	startTime := time.Now()
	apiResult, err := apiClient.BindCard(ctx, msg.CardNumber, msg.CardPassword, msg.Debug, msg.Amount)
	duration := time.Since(startTime)

	if err != nil {
		p.logger.Error("绑卡API调用失败",
			zap.String("trace_id", msg.TraceID),
			zap.Uint("ck_id", ckID),
			zap.Error(err),
			zap.Duration("duration", duration))

		return &BindCardResult{
			TraceID:      msg.TraceID,
			RecordID:     msg.RecordID,
			CKID:         ckID,
			Success:      false,
			Message:      fmt.Sprintf("API调用失败: %v", err),
			ActualAmount: 0,
		}, err
	}

	// 记录API调用成功
	p.logger.Info("绑卡API调用完成",
		zap.String("trace_id", msg.TraceID),
		zap.Uint("ck_id", ckID),
		zap.Bool("success", apiResult.Success),
		zap.Duration("duration", duration))

	// 转换API结果为内部结果格式
	result := &BindCardResult{
		TraceID:        msg.TraceID,
		RecordID:       msg.RecordID,
		CKID:           ckID,
		Success:        apiResult.Success,
		Message:        apiResult.Message,
		ActualAmount:   msg.Amount, // 使用请求中的金额，实际项目中可能需要从API响应中获取
		RawAPIResponse: apiResult,  // 🔧 保存原始API响应数据
	}
	// 如果绑卡成功，可以选择查询实际金额
	if apiResult.Success && apiResult.Balance != "" {
		// 如果API返回了余额信息，使用API返回的余额
		// 这里可以根据实际需要进行金额解析和验证
		p.logger.Info("绑卡成功，获取到余额信息",
			zap.String("trace_id", msg.TraceID),
			zap.String("balance", apiResult.Balance))
	}
	
	return result, nil
}

// handleBindCardError 处理绑卡错误
func (p *BindCardProcessor) handleBindCardError(ctx context.Context, msg *BindCardMessage, err error) error {
	p.logger.Error("绑卡处理失败",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.Int("retry_count", msg.RetryCount),
		zap.Error(err))

	// 检查重试策略服务是否可用
	if p.retryStrategy == nil {
		p.logger.Warn("重试策略服务不可用，直接更新状态为失败")
		// 更新数据库状态为失败
		if updateErr := p.updateCardRecordToFailed(ctx, msg, err); updateErr != nil {
			p.logger.Error("更新卡记录状态为失败时出错", zap.Error(updateErr))
		}
		return p.sendFailureNotification(ctx, msg, err)
	}

	// 评估是否需要重试（完全由配置文件控制）
	decision := p.retryStrategy.EvaluateBindCardRetry(ctx, err.Error(), "", msg.RetryCount)

	p.logger.Info("重试策略评估结果",
		zap.String("trace_id", msg.TraceID),
		zap.Bool("should_retry", decision.ShouldRetry),
		zap.Bool("should_switch_ck", decision.ShouldSwitchCK),
		zap.String("error_type", decision.ErrorType),
		zap.String("strategy", decision.Strategy),
		zap.String("reason", decision.Reason),
		zap.Duration("retry_delay", decision.RetryDelay))

	if decision.ShouldRetry && msg.RetryCount < msg.MaxRetries {
		// 检查是否需要切换CK
		if decision.ShouldSwitchCK {
			p.logger.Info("需要切换CK重试",
				zap.String("trace_id", msg.TraceID),
				zap.String("error_type", decision.ErrorType))
			// 这里可以添加CK切换逻辑，或者让上层处理
		}

		// 发送到延时重试队列
		return p.sendToRetryQueue(ctx, msg, decision.RetryDelay)
	}

	// 不重试，更新数据库状态为失败
	if updateErr := p.updateCardRecordToFailed(ctx, msg, err); updateErr != nil {
		p.logger.Error("更新卡记录状态为失败时出错", zap.Error(updateErr))
	}

	// 发送失败通知
	return p.sendFailureNotification(ctx, msg, err)
}

// sendToRetryQueue 发送到延时重试队列
func (p *BindCardProcessor) sendToRetryQueue(ctx context.Context, msg *BindCardMessage, delay time.Duration) error {
	msg.RetryCount++

	p.logger.Info("发送绑卡任务到重试队列",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.Int("retry_count", msg.RetryCount),
		zap.Duration("delay", delay))

	// 使用延时后直接重新处理，避免复杂的队列管理
	go func() {
		// 创建新的上下文，避免父上下文取消影响重试
		retryCtx := context.Background()

		// 等待延时
		time.Sleep(delay)

		// 在重试前再次检查去重，基于数据库状态避免重复处理
		if isDuplicate, err := p.checkMessageDuplicationByDBStatus(retryCtx, msg); err != nil {
			p.logger.Error("重试前检查消息去重失败",
				zap.String("trace_id", msg.TraceID),
				zap.String("record_id", msg.RecordID),
				zap.Error(err))
			return
		} else if isDuplicate {
			p.logger.Info("重试时发现消息已成功处理，跳过重试",
				zap.String("trace_id", msg.TraceID),
				zap.String("record_id", msg.RecordID))
			return
		}

		// 重新处理绑卡消息
		if err := p.ProcessBindCard(retryCtx, msg); err != nil {
			p.logger.Error("重试绑卡处理失败",
				zap.String("trace_id", msg.TraceID),
				zap.String("record_id", msg.RecordID),
				zap.Int("retry_count", msg.RetryCount),
				zap.Error(err))
		}
	}()

	return nil
}

// 注意：isSystemError 和 isConnectionError 方法已移除
// 所有错误判断现在完全由配置文件控制，通过 RetryStrategyService 处理

// sendFailureNotification 发送失败通知
func (p *BindCardProcessor) sendFailureNotification(ctx context.Context, msg *BindCardMessage, err error) error {
	// 暂时禁用失败通知功能，避免创建临时队列
	// TODO: 如果需要通知功能，应该通过QueueManager实现
	p.logger.Info("失败通知功能已禁用，避免创建临时队列",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.String("error", err.Error()))
	
	return nil
}

// checkMessageDuplication 检查消息去重（基于Redis）
func (p *BindCardProcessor) checkMessageDuplication(ctx context.Context, msg *BindCardMessage) (bool, error) {
	key := fmt.Sprintf("dedup:bind_card:%s:%s", msg.TraceID, msg.RecordID)
	exists, err := p.redis.Exists(ctx, key).Result()
	return exists > 0, err
}

// checkMessageDuplicationByDBStatus 基于数据库记录状态检查消息去重
func (p *BindCardProcessor) checkMessageDuplicationByDBStatus(ctx context.Context, msg *BindCardMessage) (bool, error) {
	var record struct {
		Status string `gorm:"column:status"`
	}

	err := p.db.Table("card_records").
		Select("status").
		Where("id = ? AND merchant_id = ?", msg.RecordID, msg.MerchantID).
		First(&record).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 记录不存在，不是重复消息
			return false, nil
		}
		return false, fmt.Errorf("查询记录状态失败: %w", err)
	}

	// 如果记录状态已经是成功，则认为是重复消息
	if record.Status == "success" {
		p.logger.Info("发现已成功处理的记录，跳过重复处理",
			zap.String("trace_id", msg.TraceID),
			zap.String("record_id", msg.RecordID),
			zap.String("status", record.Status))
		return true, nil
	}

	// 其他状态（processing, failed等）允许重新处理
	return false, nil
}

// markMessageProcessed 标记消息已处理
func (p *BindCardProcessor) markMessageProcessed(ctx context.Context, msg *BindCardMessage) error {
	key := fmt.Sprintf("dedup:bind_card:%s:%s", msg.TraceID, msg.RecordID)
	return p.redis.Set(ctx, key, "processed", p.deduplicationTTL).Err()
}

// BindCardResult 绑卡结果
type BindCardResult struct {
	TraceID         string      `json:"trace_id"`
	RecordID        string      `json:"record_id"`
	CKID            uint        `json:"ck_id"`
	Success         bool        `json:"success"`
	Message         string      `json:"message"`
	ErrorCode       string      `json:"error_code,omitempty"`
	ActualAmount    int         `json:"actual_amount"`
	RawAPIResponse  interface{} `json:"raw_api_response,omitempty"` // 🆕 原始API响应数据
}

// initializeQueues 初始化队列
func (p *BindCardProcessor) initializeQueues() error {
	// 队列初始化由QueueManager统一管理，这里只做健康检查
	// 获取队列名称，优先使用Queues配置
	queueName := p.config.RabbitMQ.QueueBindCard
	if p.config.RabbitMQ.Queues != nil {
		if bindCardQueue, exists := p.config.RabbitMQ.Queues["bind_card"]; exists {
			queueName = bindCardQueue
		}
	}

	p.logger.Info("检查队列管理器状态", zap.String("queue", queueName))

	// 检查队列管理器是否健康
	if !p.queueManager.IsHealthy() {
		return fmt.Errorf("队列管理器不健康，无法启动消费者")
	}

	p.logger.Info("队列管理器健康检查通过，准备启动消费者",
		zap.String("queue", queueName),
		zap.String("status", "使用统一队列管理器，避免重复连接"))

	return nil
}

// startBindCardConsumer 启动绑卡消息消费者
func (p *BindCardProcessor) startBindCardConsumer(ctx context.Context) {
	p.logger.Info("启动绑卡消费者，使用队列管理器")

	// 使用队列管理器的消费者，结合协程池处理
	err := p.queueManager.ConsumeBindCard(ctx, func(msg *queue.BindCardMessage) error {
		// 转换消息格式
		bindCardMsg := &BindCardMessage{
			TraceID:      msg.MerchantOrderID, // 使用merchant_order_id作为trace_id
			RecordID:     msg.RecordID,
			MerchantID:   msg.MerchantID,
			DepartmentID: msg.DepartmentID,
			CardNumber:   msg.CardNumber,
			CardPassword: msg.CardPassword,
			Amount:       msg.Amount,
			RetryCount:   0,        // 初始重试次数为0
			MaxRetries:   3,        // 默认最大重试次数
			CreatedAt:    msg.CreatedAt,
			Debug:        msg.Debug,
		}

		// 创建绑卡处理任务
		task := &BindCardTask{
			Message:   bindCardMsg,
			Processor: p,
			Context:   ctx,
		}

		// 提交任务到协程池
		return p.goroutinePool.Submit(task)
	})

	if err != nil {
		p.logger.Error("启动绑卡消费者失败", zap.Error(err))
		return
	}

	p.logger.Info("绑卡消费者已启动，使用队列管理器处理消息")
}

// startRetryConsumer 启动重试消息消费者（已禁用）
func (p *BindCardProcessor) startRetryConsumer(ctx context.Context) {
	// 重试消费者已禁用，避免创建临时队列
	p.logger.Info("重试消费者已禁用，避免创建临时队列")
}

// handleBindCardMessage 处理绑卡消息
func (p *BindCardProcessor) handleBindCardMessage(ctx context.Context, delivery amqp.Delivery) {
	var msg BindCardMessage
	if err := json.Unmarshal(delivery.Body, &msg); err != nil {
		p.logger.Error("解析绑卡消息失败", zap.Error(err))
		// JSON解析错误是永久性错误，拒绝消息且不重新入队
		delivery.Nack(false, false)
		return
	}

	// 处理绑卡
	if err := p.ProcessBindCard(ctx, &msg); err != nil {
		p.logger.Error("处理绑卡消息失败",
			zap.String("trace_id", msg.TraceID),
			zap.Error(err))

		// 判断错误类型，决定是否重新入队
		if p.shouldRetryError(err) {
			p.logger.Warn("绑卡处理失败，消息将重新入队重试",
				zap.String("trace_id", msg.TraceID),
				zap.Error(err))
			// 重新入队，让消息稍后重试
			delivery.Nack(false, true)
		} else {
			p.logger.Error("绑卡处理失败，消息被拒绝（不可重试的错误）",
				zap.String("trace_id", msg.TraceID),
				zap.Error(err))
			// 不可重试的错误，拒绝消息且不重新入队
			delivery.Nack(false, false)
		}
		return
	}

	// 确认消息
	delivery.Ack(false)
}

// shouldRetryError 判断错误是否应该重试（使用配置文件控制）
func (p *BindCardProcessor) shouldRetryError(err error) bool {
	if err == nil {
		return false
	}

	// 使用重试策略服务进行判断，而不是硬编码
	if p.retryStrategy == nil {
		p.logger.Warn("重试策略服务不可用，默认不重试")
		return false
	}

	decision := p.retryStrategy.EvaluateBindCardRetry(context.Background(), err.Error(), "", 0)
	return decision.ShouldRetry
}

// handleRetryMessage 处理重试消息
func (p *BindCardProcessor) handleRetryMessage(ctx context.Context, delivery amqp.Delivery) {
	var msg BindCardMessage
	if err := json.Unmarshal(delivery.Body, &msg); err != nil {
		p.logger.Error("解析重试消息失败", zap.Error(err))
		delivery.Nack(false, false)
		return
	}

	p.logger.Info("处理重试消息",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.Int("retry_count", msg.RetryCount))

	// 重新处理绑卡
	if err := p.ProcessBindCard(ctx, &msg); err != nil {
		p.logger.Error("处理重试消息失败",
			zap.String("trace_id", msg.TraceID),
			zap.Error(err))

		// 重试消息失败后，检查是否还能继续重试
		if msg.RetryCount < msg.MaxRetries && p.shouldRetryError(err) {
			p.logger.Warn("重试消息处理失败，将再次重新入队",
				zap.String("trace_id", msg.TraceID),
				zap.Int("retry_count", msg.RetryCount),
				zap.Int("max_retries", msg.MaxRetries),
				zap.Error(err))
			// 重新入队继续重试
			delivery.Nack(false, true)
		} else {
			p.logger.Error("重试消息处理最终失败，消息被拒绝",
				zap.String("trace_id", msg.TraceID),
				zap.Int("retry_count", msg.RetryCount),
				zap.Int("max_retries", msg.MaxRetries),
				zap.Error(err))
			// 达到最大重试次数或不可重试错误，拒绝消息
			delivery.Nack(false, false)
		}
		return
	}

	delivery.Ack(false)
}

// updateBindCardRecordStatus 更新绑卡记录状态
func (p *BindCardProcessor) updateBindCardRecordStatus(ctx context.Context, msg *BindCardMessage, result *BindCardResult, ckID uint, departmentID uint, startTime time.Time) error {
	// 🔧 修复：获取记录的真实创建时间来计算准确的处理时间
	var processTime float64
	var record model.CardRecord
	if err := p.db.WithContext(ctx).Where("id = ?", msg.RecordID).First(&record).Error; err != nil {
		p.logger.Error("获取记录创建时间失败，使用传入的startTime",
			zap.String("record_id", msg.RecordID),
			zap.Error(err))
		// 如果获取失败，使用传入的startTime作为备选
		processTime = time.Since(startTime).Seconds()
	} else {
		// 使用记录的真实创建时间计算处理时间
		processTime = time.Since(record.CreatedAt).Seconds()
		p.logger.Info("使用记录创建时间计算处理时间",
			zap.String("record_id", msg.RecordID),
			zap.Time("created_at", record.CreatedAt),
			zap.Float64("process_time", processTime))
	}

	// 准备更新数据
	updateData := map[string]interface{}{
		"updated_at":    time.Now(),
		"process_time":  processTime,
		"department_id": departmentID,
	}

	// 🔧 修复：使用原始API响应数据，而不是处理过的数据
	var responseData interface{}
	if result.RawAPIResponse != nil {
		// 使用原始API响应数据
		responseData = result.RawAPIResponse
	} else {
		// 如果没有原始响应数据，使用处理过的数据作为备选
		responseData = map[string]interface{}{
			"success":      result.Success,
			"message":      result.Message,
			"timestamp":    time.Now().Format(time.RFC3339),
			"ck_id":        result.CKID,
			"process_time": processTime,
		}
		if !result.Success {
			responseData.(map[string]interface{})["error"] = result.Message
		}
	}

	if result.Success {
		// 绑卡成功
		updateData["status"] = "success"
		updateData["walmart_ck_id"] = ckID
		updateData["actual_amount"] = result.ActualAmount
		updateData["error_message"] = nil

		// 测试模式下的余额字段处理
		if msg.Debug {
			// 注意：真实绑卡接口不返回金额信息，金额需要通过获取卡包接口获取
			// 测试模式下暂时留空，等待后续通过获取卡包接口填充
			updateData["balance"] = ""      // 绑卡接口不返回，留空
			updateData["cardBalance"] = ""  // 绑卡接口不返回，留空
			updateData["balanceCnt"] = ""   // 绑卡接口不返回，留空
		}

		p.logger.Info("更新绑卡记录为成功状态",
			zap.String("trace_id", msg.TraceID),
			zap.String("record_id", msg.RecordID),
			zap.Uint("ck_id", ckID),
			zap.Uint("department_id", departmentID),
			zap.Float64("process_time", processTime))
	} else {
		// 绑卡失败
		updateData["status"] = "failed"
		updateData["error_message"] = result.Message
		updateData["walmart_ck_id"] = ckID // 🔧 修复：失败时也要保存CK ID

		p.logger.Info("更新绑卡记录为失败状态",
			zap.String("trace_id", msg.TraceID),
			zap.String("record_id", msg.RecordID),
			zap.String("error", result.Message),
			zap.Uint("ck_id", ckID), // 🔧 修复：记录使用的CK ID
			zap.Uint("department_id", departmentID),
			zap.Float64("process_time", processTime))
	}

	// 序列化响应数据
	if responseDataJSON, err := json.Marshal(responseData); err == nil {
		updateData["response_data"] = string(responseDataJSON)
	}

	// 执行数据库更新
	result_db := p.db.WithContext(ctx).Table("card_records").
		Where("id = ?", msg.RecordID).
		Updates(updateData)

	if result_db.Error != nil {
		return fmt.Errorf("数据库更新失败: %w", result_db.Error)
	}

	if result_db.RowsAffected == 0 {
		return fmt.Errorf("未找到记录ID: %s", msg.RecordID)
	}

	p.logger.Info("绑卡记录状态更新成功",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.Int64("rows_affected", result_db.RowsAffected))

	return nil
}

// updateBindCardRecordStatusWithoutProcessTime 更新绑卡记录状态（不包含最终process_time）
func (p *BindCardProcessor) updateBindCardRecordStatusWithoutProcessTime(ctx context.Context, msg *BindCardMessage, result *BindCardResult, ckID uint, departmentID uint) error {
	// 准备更新数据（不包含process_time）
	updateData := map[string]interface{}{
		"updated_at":    time.Now(),
		"department_id": departmentID,
	}

	// 🔧 修复：使用原始API响应数据，而不是处理过的数据
	var responseData interface{}
	if result.RawAPIResponse != nil {
		// 使用原始API响应数据
		responseData = result.RawAPIResponse
	} else {
		// 如果没有原始响应数据，使用处理过的数据作为备选
		responseData = map[string]interface{}{
			"success":    result.Success,
			"message":    result.Message,
			"timestamp":  time.Now().Format(time.RFC3339),
			"ck_id":      result.CKID,
		}
		if !result.Success {
			responseData.(map[string]interface{})["error"] = result.Message
		}
	}

	if result.Success {
		// 绑卡成功
		updateData["status"] = "success"
		updateData["walmart_ck_id"] = ckID

		// 注意：BindCardResult 不包含余额字段，余额信息通过 fetchCardBalance 获取

		p.logger.Info("更新绑卡记录为成功状态（不含最终process_time）",
			zap.String("trace_id", msg.TraceID),
			zap.String("record_id", msg.RecordID),
			zap.Uint("ck_id", ckID),
			zap.Uint("department_id", departmentID))
	} else {
		// 绑卡失败
		updateData["status"] = "failed"
		updateData["error_message"] = result.Message
		updateData["walmart_ck_id"] = ckID // 🔧 修复：失败时也要保存CK ID

		p.logger.Info("更新绑卡记录为失败状态（不含最终process_time）",
			zap.String("trace_id", msg.TraceID),
			zap.String("record_id", msg.RecordID),
			zap.String("error", result.Message),
			zap.Uint("ck_id", ckID), // 🔧 修复：记录使用的CK ID
			zap.Uint("department_id", departmentID))
	}

	// 序列化响应数据
	if responseDataJSON, err := json.Marshal(responseData); err == nil {
		updateData["response_data"] = string(responseDataJSON)
	}

	// 执行数据库更新
	dbResult := p.db.WithContext(ctx).Model(&model.CardRecord{}).
		Where("id = ?", msg.RecordID).
		Updates(updateData)

	if dbResult.Error != nil {
		return fmt.Errorf("更新绑卡记录状态失败: %w", dbResult.Error)
	}

	if dbResult.RowsAffected == 0 {
		return fmt.Errorf("没有找到要更新的记录，record_id: %s", msg.RecordID)
	}

	return nil
}

// updateFinalProcessTime 更新最终的准确处理时间
func (p *BindCardProcessor) updateFinalProcessTime(ctx context.Context, msg *BindCardMessage) error {
	// 获取记录的创建时间
	var record model.CardRecord
	if err := p.db.WithContext(ctx).Where("id = ?", msg.RecordID).First(&record).Error; err != nil {
		return fmt.Errorf("获取记录失败: %w", err)
	}

	// 计算从创建到现在的准确处理时间
	processTime := time.Since(record.CreatedAt).Seconds()

	// 只更新process_time和updated_at
	updateData := map[string]interface{}{
		"process_time": processTime,
		"updated_at":   time.Now(),
	}

	result := p.db.WithContext(ctx).Model(&model.CardRecord{}).
		Where("id = ?", msg.RecordID).
		Updates(updateData)

	if result.Error != nil {
		return fmt.Errorf("更新最终处理时间失败: %w", result.Error)
	}

	p.logger.Info("已更新最终准确的处理时间",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.Float64("final_process_time", processTime))

	return nil
}

// shouldSwitchCK 判断是否需要进行CK故障切换（使用配置文件控制）
func (p *BindCardProcessor) shouldSwitchCK(bindResult *BindCardResult) bool {
	if bindResult == nil {
		return false
	}

	// 使用重试策略服务进行判断，而不是硬编码
	if p.retryStrategy == nil {
		p.logger.Warn("重试策略服务不可用，默认不切换CK")
		return false
	}

	decision := p.retryStrategy.EvaluateBindCardRetry(context.Background(), bindResult.Message, "", 0)
	return decision.ShouldSwitchCK
}

// handleCKFailureAndSwitch 处理CK故障并自动切换
func (p *BindCardProcessor) handleCKFailureAndSwitch(ctx context.Context, msg *BindCardMessage, failedCKID uint, originalErr error) error {
	p.logger.Warn("检测到CK故障，开始自动切换",
		zap.String("trace_id", msg.TraceID),
		zap.Uint("failed_ck_id", failedCKID),
		zap.String("error_message", originalErr.Error()))

	// 1. 记录CK故障日志到数据库
	if err := p.recordCKFailureLog(ctx, msg, failedCKID, originalErr.Error()); err != nil {
		p.logger.Error("记录CK故障日志失败", zap.Error(err))
	}

	

	// 3. 禁用故障CK（带重试机制）
	if err := p.disableCKWithRetry(ctx, failedCKID, fmt.Sprintf("绑卡失败自动禁用: %s", originalErr.Error())); err != nil {
		p.logger.Error("禁用故障CK失败，但已加入内存黑名单",
			zap.Uint("failed_ck_id", failedCKID),
			zap.Error(err))
		// 🔧 修复：即使禁用失败，也要确保后续选择CK时排除故障CK
	}

	// 3. 重新选择部门和CK（确保不选择故障CK）
	var newRecord *CKPreoccupationRecord
	var dept *model.Department
	// 🔧 修复：从配置文件读取重试次数，而不是硬编码
	maxAttempts := p.config.RetryStrategy.BindCard.MaxAttempts
	if maxAttempts <= 0 {
		maxAttempts = 3 // 默认值
	}

	// 🔧 新增：跨部门CK切换逻辑
	for attempt := 0; attempt < maxAttempts; attempt++ {
		// 选择部门
		selectedDept, err := p.weightManager.SelectDepartmentByWeight(ctx, msg.MerchantID)
		if err != nil {
			// 如果权重算法失败，尝试直接查找任何可用CK
			p.logger.Warn("权重算法选择部门失败，尝试直接查找可用CK",
				zap.String("trace_id", msg.TraceID),
				zap.Int("attempt", attempt+1),
				zap.Error(err))

			// 直接查找商户下任何可用的CK（跨部门）
			availableCK, ckErr := p.findAnyAvailableCK(ctx, msg.MerchantID, failedCKID)
			if ckErr != nil {
				p.logger.Warn("未找到任何可用CK",
					zap.String("trace_id", msg.TraceID),
					zap.Int("attempt", attempt+1),
					zap.Error(ckErr))
				continue
			}

			// 使用找到的CK创建预占用请求
			preoccupyReq := &CKPreoccupationRequest{
				TraceID:      msg.TraceID,
				RecordID:     msg.RecordID,
				MerchantID:   msg.MerchantID,
				DepartmentID: availableCK.DepartmentID,
				Amount:       msg.Amount,
				CardNumber:   msg.CardNumber,
			}

			// 手动创建预占用记录（绕过权重算法）
			record, preoccupyErr := p.preoccupationManager.PreoccupyCK(ctx, preoccupyReq)
			if preoccupyErr != nil {
				p.logger.Warn("直接预占用CK失败",
					zap.String("trace_id", msg.TraceID),
					zap.Uint("ck_id", availableCK.ID),
					zap.Int("attempt", attempt+1),
					zap.Error(preoccupyErr))
				continue
			}

			// 确保不是故障CK
			if record.CKID == failedCKID {
				_ = p.preoccupationManager.CommitPreoccupation(ctx, record.TraceID, record.RecordID, false, 0)
				continue
			}

			// 查询部门信息
			var foundDept model.Department
			if err := p.db.First(&foundDept, availableCK.DepartmentID).Error; err != nil {
				_ = p.preoccupationManager.CommitPreoccupation(ctx, record.TraceID, record.RecordID, false, 0)
				continue
			}

			newRecord = record
			dept = &foundDept
			p.logger.Info("跨部门CK切换成功",
				zap.String("trace_id", msg.TraceID),
				zap.Uint("old_ck_id", failedCKID),
				zap.Uint("new_ck_id", record.CKID),
				zap.Uint("new_department_id", foundDept.ID),
				zap.String("switch_mode", "cross_department"))
			break
		}

		dept = selectedDept

		// 4. 预占用新CK
		preoccupyReq := &CKPreoccupationRequest{
			TraceID:      msg.TraceID,
			RecordID:     msg.RecordID,
			MerchantID:   msg.MerchantID,
			DepartmentID: dept.ID,
			Amount:       msg.Amount,
			CardNumber:   msg.CardNumber,
		}

		record, err := p.preoccupationManager.PreoccupyCK(ctx, preoccupyReq)
		if err != nil {
			p.logger.Warn("CK切换时预占用失败，尝试下一个部门",
				zap.String("trace_id", msg.TraceID),
				zap.Uint("department_id", dept.ID),
				zap.Int("attempt", attempt+1),
				zap.Error(err))
			continue
		}

		// 🔧 关键修复：确保新选择的CK不是故障CK
		if record.CKID == failedCKID {
			p.logger.Warn("选择到了故障CK，释放预占用并重新选择",
				zap.String("trace_id", msg.TraceID),
				zap.Uint("failed_ck_id", failedCKID),
				zap.Uint("selected_ck_id", record.CKID),
				zap.Int("attempt", attempt+1))

			// 释放预占用
			_ = p.preoccupationManager.CommitPreoccupation(ctx, record.TraceID, record.RecordID, false, 0)
			continue
		}

		newRecord = record
		break
	}

	if newRecord == nil {
		// 🔧 修复：CK切换失败时，更新数据库状态为失败并计算处理时长
		ckSwitchError := fmt.Errorf("CK切换失败：尝试%d次后仍无法获取可用CK，建议检查商户CK配置（配置的最大重试次数：%d）", maxAttempts, p.config.RetryStrategy.BindCard.MaxAttempts)

		if updateErr := p.updateCardRecordToFailed(ctx, msg, ckSwitchError); updateErr != nil {
			p.logger.Error("更新CK切换失败状态时出错", zap.Error(updateErr))
		}

		return ckSwitchError
	}

	p.logger.Info("CK自动切换成功",
		zap.String("trace_id", msg.TraceID),
		zap.Uint("old_ck_id", failedCKID),
		zap.Uint("new_ck_id", newRecord.CKID),
		zap.Uint("new_department_id", dept.ID))

	// 5. 使用新CK重新执行绑卡
	bindResult, err := p.executeBindCard(ctx, msg, newRecord.CKID)
	if err != nil {
		// 回滚新CK预占用
		if commitErr := p.preoccupationManager.CommitPreoccupation(ctx, msg.TraceID, msg.RecordID, false, 0); commitErr != nil {
			p.logger.Error("回滚新CK预占用失败", zap.Error(commitErr))
		}
		return fmt.Errorf("使用新CK绑卡失败: %w", err)
	}

	// 6. 确认新CK预占用
	if err := p.preoccupationManager.CommitPreoccupation(ctx, msg.TraceID, msg.RecordID, bindResult.Success, bindResult.ActualAmount); err != nil {
		p.logger.Error("确认新CK预占用失败", zap.Error(err))
	}

	// 7. 如果绑卡成功，获取卡片金额
	if bindResult.Success {
		if err := p.fetchCardBalance(ctx, msg, newRecord.CKID); err != nil {
			p.logger.Error("获取卡片金额失败", zap.Error(err))
			// 金额获取失败不影响绑卡成功状态
		}
	}

	// 8. 更新绑卡记录
	return p.updateBindCardRecord(ctx, msg, bindResult)
}

// recordCKFailureLog 记录CK故障日志到数据库
func (p *BindCardProcessor) recordCKFailureLog(ctx context.Context, msg *BindCardMessage, ckID uint, errorMessage string) error {
	logData := map[string]interface{}{
		"ck_id":         ckID,
		"merchant_id":   msg.MerchantID,
		"trace_id":      msg.TraceID,
		"record_id":     msg.RecordID,
		"card_number":   maskCardNumber(msg.CardNumber),
		"error_message": errorMessage,
		"action":        "CK_FAILURE_DETECTED",
		"timestamp":     time.Now().Unix(),
	}

	// 插入到binding_logs表
	query := `
		INSERT INTO binding_logs (
			id, card_record_id, log_type, log_level, message, details,
			walmart_ck_id, timestamp, created_at, updated_at
		) VALUES (
			UUID(), ?, 'ck_failure', 'error', ?, ?,
			?, NOW(3), NOW(3), NOW(3)
		)
	`

	detailsJSON, _ := json.Marshal(logData)
	message := fmt.Sprintf("CK %d 故障检测: %s", ckID, errorMessage)

	err := p.db.WithContext(ctx).Exec(query,
		msg.RecordID, message, string(detailsJSON), ckID).Error

	if err != nil {
		return fmt.Errorf("插入CK故障日志失败: %w", err)
	}

	p.logger.Info("CK故障日志记录成功",
		zap.String("trace_id", msg.TraceID),
		zap.Uint("ck_id", ckID))

	return nil
}

// disableCK 禁用故障CK - 就是一条简单的SQL更新
func (p *BindCardProcessor) disableCK(ctx context.Context, ckID uint, reason string) error {
	// 直接执行SQL更新，设置active=false
	result := p.db.WithContext(ctx).Model(&model.WalmartCK{}).
		Where("id = ?", ckID).
		Update("active", false)

	if result.Error != nil {
		return fmt.Errorf("禁用CK失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("CK %d 不存在或已被禁用", ckID)
	}

	p.logger.Warn("CK已被自动禁用",
		zap.Uint("ck_id", ckID),
		zap.String("reason", reason),
		zap.Int64("rows_affected", result.RowsAffected))

	return nil
}

// disableCKWithRetry 禁用故障CK（带重试机制）
func (p *BindCardProcessor) disableCKWithRetry(ctx context.Context, ckID uint, reason string) error {
	// 🔧 修复：从配置文件读取重试次数，而不是硬编码
	maxRetries := p.config.RetryStrategy.BindCard.MaxAttempts
	if maxRetries <= 0 {
		maxRetries = 5 // 增加默认重试次数
	}
	baseDelay := 50 * time.Millisecond // 减少基础延迟

	for attempt := 0; attempt < maxRetries; attempt++ {
		err := p.disableCK(ctx, ckID, reason)
		if err == nil {
			p.logger.Info("CK禁用成功",
				zap.Uint("ck_id", ckID),
				zap.Int("attempt", attempt+1))
			return nil
		}

		// 检查是否是版本冲突错误
		if strings.Contains(err.Error(), "CK状态更新冲突") && attempt < maxRetries-1 {
			// 🔧 改进：使用随机化的指数退避，避免雷群效应
			baseDelayMs := baseDelay.Milliseconds()
			jitter := time.Duration(rand.Intn(int(baseDelayMs/2))) * time.Millisecond
			delay := baseDelay*time.Duration(1<<attempt) + jitter

			p.logger.Warn("CK禁用冲突，准备重试",
				zap.Uint("ck_id", ckID),
				zap.Int("attempt", attempt+1),
				zap.Duration("delay", delay),
				zap.Error(err))

			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(delay):
				continue
			}
		}

		// 非冲突错误或最后一次重试失败
		p.logger.Error("CK禁用失败",
			zap.Uint("ck_id", ckID),
			zap.Int("attempt", attempt+1),
			zap.Error(err))
		return err
	}

	return fmt.Errorf("禁用CK重试%d次后仍失败（配置的最大重试次数：%d）", maxRetries, p.config.RetryStrategy.BindCard.MaxAttempts)
}

// findAnyAvailableCK 查找商户下任何可用的CK（跨部门）
func (p *BindCardProcessor) findAnyAvailableCK(ctx context.Context, merchantID uint, excludeCKID uint) (*model.WalmartCK, error) {
	var candidates []model.WalmartCK

	query := p.db.WithContext(ctx).Where(
		"merchant_id = ? AND active = ? AND is_deleted = ? AND bind_count < total_limit",
		merchantID, true, false,
	)

	// 排除故障CK
	if excludeCKID > 0 {
		query = query.Where("id != ?", excludeCKID)
	}

	err := query.Order("bind_count ASC, last_bind_time ASC").Find(&candidates).Error
	if err != nil {
		return nil, fmt.Errorf("查询可用CK失败: %w", err)
	}

	if len(candidates) == 0 {
		return nil, fmt.Errorf("商户 %d 没有其他可用CK", merchantID)
	}

	// 直接返回第一个候选CK（已经通过excludeCKID排除了故障CK）
	if len(candidates) > 0 {
		candidate := candidates[0]
		p.logger.Info("找到跨部门可用CK",
			zap.Uint("merchant_id", merchantID),
			zap.Uint("ck_id", candidate.ID),
			zap.Uint("department_id", candidate.DepartmentID),
			zap.Uint("excluded_ck_id", excludeCKID),
			zap.Int("bind_count", candidate.BindCount),
			zap.Int("total_limit", candidate.TotalLimit))
		return &candidate, nil
	}

	return nil, fmt.Errorf("商户 %d 没有其他可用CK", merchantID)
}

// fetchCardBalance 获取卡片真实金额（使用与绑卡相同的CK）
func (p *BindCardProcessor) fetchCardBalance(ctx context.Context, msg *BindCardMessage, ckID uint) error {
	p.logger.Info("开始获取卡片金额",
		zap.String("trace_id", msg.TraceID),
		zap.Uint("ck_id", ckID),
		zap.String("card_number", maskCardNumber(msg.CardNumber)))

	// 获取CK信息
	var ck struct {
		ID   uint   `gorm:"column:id"`
		Sign string `gorm:"column:sign"`
	}

	if err := p.db.Table("walmart_ck").Where("id = ?", ckID).First(&ck).Error; err != nil {
		return fmt.Errorf("获取CK信息失败: %w", err)
	}

	// 创建Walmart API客户端 - API地址写死在代码中保护机密信息
	apiConfig := walmart.APIConfig{
		Sign:    ck.Sign,
		BaseURL: "https://apicard.swiftpass.cn", // 机密信息，写死在代码中
	}

	// 创建logrus.Logger实例用于APIClient
	logrusLogger := logrus.New()
	logrusLogger.SetLevel(logrus.InfoLevel)

	walmartAPI := walmart.NewAPIClientWithZap(apiConfig, logrusLogger, p.logger)

	// 重试机制获取金额
	maxRetries := 3
	retryDelay := time.Second

	for attempt := 1; attempt <= maxRetries; attempt++ {
		p.logger.Info("尝试获取卡片金额",
			zap.String("trace_id", msg.TraceID),
			zap.Int("attempt", attempt),
			zap.Int("max_retries", maxRetries))

		balanceResult, err := walmartAPI.GetCardBalance(ctx, msg.CardNumber, msg.Debug, msg.Amount)
		if err == nil && balanceResult != nil && balanceResult.Success {
			// 金额获取成功，更新数据库记录
			return p.updateCardBalanceRecord(ctx, msg, balanceResult)
		}

		p.logger.Warn("获取卡片金额失败，准备重试",
			zap.String("trace_id", msg.TraceID),
			zap.Int("attempt", attempt),
			zap.Error(err))

		if attempt < maxRetries {
			time.Sleep(retryDelay)
			retryDelay *= 2 // 指数退避
		}
	}

	return fmt.Errorf("获取卡片金额失败，已重试%d次", maxRetries)
}

// updateCardBalanceRecord 更新卡片金额记录
func (p *BindCardProcessor) updateCardBalanceRecord(ctx context.Context, msg *BindCardMessage, balanceResult *walmart.BalanceQueryResult) error {
	// 从CardList中根据卡号匹配对应的卡片信息
	var actualAmount int
	var balance, cardBalance, balanceCnt string
	var foundCard bool

	p.logger.Info("开始匹配卡片信息",
		zap.String("trace_id", msg.TraceID),
		zap.String("target_card_no", maskCardNumber(msg.CardNumber)),
		zap.Int("total_cards", len(balanceResult.CardList)))

	// 遍历所有卡片，找到匹配的卡号
	for i, cardInfo := range balanceResult.CardList {
		if cardNo, ok := cardInfo["cardNo"].(string); ok && cardNo == msg.CardNumber {
			foundCard = true
			p.logger.Info("找到匹配的卡片",
				zap.String("trace_id", msg.TraceID),
				zap.String("card_no", maskCardNumber(cardNo)),
				zap.Int("card_index", i))

			// 提取余额信息
			if bal, ok := cardInfo["balance"].(string); ok {
				balance = bal
			}
			if cardBal, ok := cardInfo["cardBalance"]; ok {
				// cardBalance可能是int或string类型
				switch v := cardBal.(type) {
				case string:
					cardBalance = v
				case int:
					cardBalance = fmt.Sprintf("%d", v)
				case float64:
					cardBalance = fmt.Sprintf("%.0f", v)
				}
			}
			if balCnt, ok := cardInfo["balanceCnt"].(string); ok {
				balanceCnt = balCnt
			}

			// 解析实际金额（从cardBalance字段，单位为分）
			if cardBalance != "" {
				if amount, err := strconv.Atoi(cardBalance); err == nil {
					actualAmount = amount
				}
			}

			p.logger.Info("成功提取卡片余额信息",
				zap.String("trace_id", msg.TraceID),
				zap.String("balance", balance),
				zap.String("cardBalance", cardBalance),
				zap.String("balanceCnt", balanceCnt),
				zap.Int("actualAmount", actualAmount))
			break
		}
	}

	if !foundCard {
		p.logger.Error("未找到匹配的卡片",
			zap.String("trace_id", msg.TraceID),
			zap.String("target_card_no", maskCardNumber(msg.CardNumber)),
			zap.Int("total_cards", len(balanceResult.CardList)))
		return fmt.Errorf("未找到卡号为 %s 的卡片信息", maskCardNumber(msg.CardNumber))
	}

	// 首先获取当前的response_data
	var currentRecord model.CardRecord
	if err := p.db.WithContext(ctx).Where("id = ?", msg.RecordID).First(&currentRecord).Error; err != nil {
		return fmt.Errorf("获取当前记录失败: %w", err)
	}

	p.logger.Info("获取到当前记录",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.String("current_balance", func() string {
			if currentRecord.Balance != nil {
				return *currentRecord.Balance
			}
			return "NULL"
		}()),
		zap.String("current_cardBalance", func() string {
			if currentRecord.CardBalance != nil {
				return *currentRecord.CardBalance
			}
			return "NULL"
		}()),
		zap.String("current_balanceCnt", func() string {
			if currentRecord.BalanceCnt != nil {
				return *currentRecord.BalanceCnt
			}
			return "NULL"
		}()))

	// 解析现有的response_data
	var responseData map[string]interface{}
	if currentRecord.ResponseData != nil && *currentRecord.ResponseData != "" {
		if err := json.Unmarshal([]byte(*currentRecord.ResponseData), &responseData); err != nil {
			p.logger.Warn("解析现有response_data失败，创建新的", zap.Error(err))
			responseData = make(map[string]interface{})
		}
	} else {
		responseData = make(map[string]interface{})
	}

	// 更新response_data中的余额信息
	responseData["balance"] = balance
	responseData["cardBalance"] = cardBalance
	responseData["balanceCnt"] = balanceCnt

	// 序列化更新后的response_data
	responseDataJSON, err := json.Marshal(responseData)
	if err != nil {
		return fmt.Errorf("序列化response_data失败: %w", err)
	}
	responseDataStr := string(responseDataJSON)

	// 🔧 修复：保留现有的process_time，避免被覆盖
	updateData := map[string]interface{}{
		"actual_amount": actualAmount,
		"balance":       balance,
		"cardBalance":   cardBalance,
		"balanceCnt":    balanceCnt,
		"response_data": responseDataStr,
		"updated_at":    time.Now(),
		// 注意：不更新process_time，保留之前设置的值
	}

	p.logger.Info("准备更新数据库",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.String("update_balance", balance),
		zap.String("update_cardBalance", cardBalance),
		zap.String("update_balanceCnt", balanceCnt),
		zap.String("response_data_preview", responseDataStr[:min(200, len(responseDataStr))]))

	result := p.db.WithContext(ctx).Model(&model.CardRecord{}).
		Where("id = ?", msg.RecordID).
		Updates(updateData)

	if result.Error != nil {
		return fmt.Errorf("更新卡片金额记录失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("未找到记录ID: %s，无法更新余额信息", msg.RecordID)
	}

	p.logger.Info("卡片金额更新成功",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.Int64("rows_affected", result.RowsAffected),
		zap.Int("actual_amount", actualAmount),
		zap.String("balance", balance),
		zap.String("cardBalance", cardBalance),
		zap.String("balanceCnt", balanceCnt))

	// 🔧 新增：金额验证和回调消息发送逻辑
	if err := p.validateAmountAndSendCallback(ctx, msg, actualAmount); err != nil {
		p.logger.Error("金额验证或回调消息发送失败",
			zap.String("trace_id", msg.TraceID),
			zap.String("record_id", msg.RecordID),
			zap.Error(err))
		// 注意：金额验证失败不影响金额更新的成功状态
	}

	return nil
}

// updateBindCardRecord 更新绑卡记录状态
func (p *BindCardProcessor) updateBindCardRecord(ctx context.Context, msg *BindCardMessage, result *BindCardResult) error {
	// 🔧 修复：首先获取记录的创建时间以计算处理时长
	var record model.CardRecord
	if err := p.db.WithContext(ctx).Where("id = ?", msg.RecordID).First(&record).Error; err != nil {
		return fmt.Errorf("获取记录失败: %w", err)
	}

	// 🔧 修复：计算处理时长（从创建时间到现在）
	processTime := time.Since(record.CreatedAt).Seconds()

	status := "failed"
	if result.Success {
		status = "success"
	}

	updateData := map[string]interface{}{
		"status":        status,
		"walmart_ck_id": result.CKID,
		"process_time":  processTime, // 🔧 修复：添加处理时长
		"updated_at":    time.Now(),
	}

	if result.Success {
		updateData["actual_amount"] = result.ActualAmount
	} else {
		updateData["error_message"] = result.Message
	}

	dbResult := p.db.WithContext(ctx).Model(&model.CardRecord{}).
		Where("id = ?", msg.RecordID).
		Updates(updateData)

	if dbResult.Error != nil {
		return fmt.Errorf("更新绑卡记录失败: %w", dbResult.Error)
	}

	p.logger.Info("绑卡记录更新成功",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.String("status", status),
		zap.Float64("process_time", processTime)) // 🔧 修复：记录处理时长

	return nil
}

// recordBindingLog 记录绑卡日志到binding_logs表
func (p *BindCardProcessor) recordBindingLog(ctx context.Context, msg *BindCardMessage, action, status, message string, startTime, endTime time.Time, responseData interface{}) error {
	return p.recordBindingLogWithDepartment(ctx, msg, action, status, message, startTime, endTime, responseData, nil)
}

// recordBindingLogWithDepartment 记录绑卡日志到binding_logs表（带部门ID）
func (p *BindCardProcessor) recordBindingLogWithDepartment(ctx context.Context, msg *BindCardMessage, action, status, message string, startTime, endTime time.Time, responseData interface{}, departmentID *uint) error {
	// 计算处理时间
	var durationMS *float64
	if !endTime.IsZero() && !startTime.IsZero() {
		duration := float64(endTime.Sub(startTime).Nanoseconds()) / 1e6 // 转换为毫秒
		durationMS = &duration
	}

	// 序列化请求数据
	requestDataJSON, _ := json.Marshal(map[string]interface{}{
		"trace_id":      msg.TraceID,
		"record_id":     msg.RecordID,
		"merchant_id":   msg.MerchantID,
		"card_number":   maskCardNumber(msg.CardNumber),
		"amount":        msg.Amount,
		"retry_count":   msg.RetryCount,
		"debug":         msg.Debug,
	})

	// 序列化响应数据
	var responseDataJSON []byte
	if responseData != nil {
		responseDataJSON, _ = json.Marshal(responseData)
	}

	// 序列化详细信息（包含action和status信息）
	detailsData := map[string]interface{}{
		"action":        action,
		"status":        status,
		"trace_id":      msg.TraceID,
		"merchant_id":   msg.MerchantID,
		"retry_count":   msg.RetryCount,
	}
	detailsJSON, _ := json.Marshal(detailsData)

	// 转换字符串指针
	var requestDataStr, responseDataStr, detailsStr *string
	if len(requestDataJSON) > 0 {
		str := string(requestDataJSON)
		requestDataStr = &str
	}
	if len(responseDataJSON) > 0 {
		str := string(responseDataJSON)
		responseDataStr = &str
	}
	if len(detailsJSON) > 0 {
		str := string(detailsJSON)
		detailsStr = &str
	}

	// 生成UUID - 使用标准UUID库确保线程安全和唯一性
	id := uuid.New().String()

	// 创建绑卡日志记录
	merchantID := msg.MerchantID
	bindingLog := &model.BindingLog{
		ID:           id,
		CardRecordID: msg.RecordID,
		MerchantID:   &merchantID,
		DepartmentID: departmentID, // 使用传入的部门ID
		LogType:      "binding",
		LogLevel:     "info",
		Message:      message,
		Details:      detailsStr,
		RequestData:  requestDataStr,
		ResponseData: responseDataStr,
		DurationMS:   durationMS,
		Timestamp:    time.Now(),
	}

	// 保存到数据库
	if err := p.db.WithContext(ctx).Create(bindingLog).Error; err != nil {
		return fmt.Errorf("保存绑卡日志失败: %w", err)
	}

	p.logger.Info("绑卡日志记录成功",
		zap.String("trace_id", msg.TraceID),
		zap.String("action", action),
		zap.String("status", status),
		zap.Float64p("duration_ms", durationMS))

	return nil
}

// recordSystemLog 记录系统日志
func (p *BindCardProcessor) recordSystemLog(ctx context.Context, msg *BindCardMessage, message string, details map[string]interface{}) error {
	return p.recordLog(ctx, msg, "system", "info", message, details, nil, nil, nil, nil, nil)
}

// recordWalmartRequestLog 记录沃尔玛API请求日志
func (p *BindCardProcessor) recordWalmartRequestLog(ctx context.Context, msg *BindCardMessage, attemptNumber int, walmartCKID uint, requestData interface{}) error {
	return p.recordWalmartRequestLogWithDepartment(ctx, msg, attemptNumber, walmartCKID, requestData, nil)
}

// recordWalmartRequestLogWithDepartment 记录沃尔玛API请求日志（带部门ID）
func (p *BindCardProcessor) recordWalmartRequestLogWithDepartment(ctx context.Context, msg *BindCardMessage, attemptNumber int, walmartCKID uint, requestData interface{}, departmentID *uint) error {
	details := map[string]interface{}{
		"trace_id":              msg.TraceID,
		"merchant_id":           msg.MerchantID,
		"walmart_ck_id":         walmartCKID,
		"attempt_number":        attemptNumber,
		"attempt_start_time":    time.Now().Format(time.RFC3339),
		"card_number_masked":    maskCardNumber(msg.CardNumber),
	}

	if departmentID != nil {
		details["department_id"] = *departmentID
	}

	attemptStr := fmt.Sprintf("%d", attemptNumber)
	return p.recordLogWithDepartment(ctx, msg, "walmart_request", "info",
		fmt.Sprintf("开始绑卡尝试 #%d | CK_ID=%d | 卡号=%s", attemptNumber, walmartCKID, maskCardNumber(msg.CardNumber)),
		details, requestData, nil, nil, &attemptStr, &walmartCKID, departmentID)
}

// recordWalmartResponseLog 记录沃尔玛API响应日志
func (p *BindCardProcessor) recordWalmartResponseLog(ctx context.Context, msg *BindCardMessage, attemptNumber int, walmartCKID uint, responseData interface{}, durationMS float64) error {
	return p.recordWalmartResponseLogWithDepartment(ctx, msg, attemptNumber, walmartCKID, responseData, durationMS, nil)
}

// recordWalmartResponseLogWithDepartment 记录沃尔玛API响应日志（带部门ID）
func (p *BindCardProcessor) recordWalmartResponseLogWithDepartment(ctx context.Context, msg *BindCardMessage, attemptNumber int, walmartCKID uint, responseData interface{}, durationMS float64, departmentID *uint) error {
	attemptStr := fmt.Sprintf("%d", attemptNumber)
	return p.recordLogWithDepartment(ctx, msg, "walmart_response", "info",
		fmt.Sprintf("沃尔玛API响应 (尝试 %d)", attemptNumber),
		nil, nil, responseData, &durationMS, &attemptStr, &walmartCKID, departmentID)
}

// recordBindAttemptLog 记录绑卡尝试结果日志
func (p *BindCardProcessor) recordBindAttemptLog(ctx context.Context, msg *BindCardMessage, attemptNumber int, walmartCKID uint, success bool, durationMS float64, responseData interface{}) error {
	return p.recordBindAttemptLogWithDepartment(ctx, msg, attemptNumber, walmartCKID, success, durationMS, responseData, nil)
}

// recordBindAttemptLogWithDepartment 记录绑卡尝试结果日志（带部门ID）
func (p *BindCardProcessor) recordBindAttemptLogWithDepartment(ctx context.Context, msg *BindCardMessage, attemptNumber int, walmartCKID uint, success bool, durationMS float64, responseData interface{}, departmentID *uint) error {
	details := map[string]interface{}{
		"log_id":              generateLogID(),
		"success":             success,
		"trace_id":            msg.TraceID,
		"error_code":          nil,
		"duration_ms":         durationMS,
		"merchant_id":         msg.MerchantID,
		"error_source":        "walmart_api",
		"raw_response":        responseData,
		"error_message":       nil,
		"walmart_ck_id":       walmartCKID,
		"attempt_number":      attemptNumber,
		"response_status":     success,
		"attempt_end_time":    time.Now().Format(time.RFC3339),
	}

	if departmentID != nil {
		details["department_id"] = *departmentID
	}

	if !success {
		if resp, ok := responseData.(map[string]interface{}); ok {
			if errorInfo, exists := resp["error"]; exists {
				if errorMap, ok := errorInfo.(map[string]interface{}); ok {
					details["error_message"] = errorMap["message"]
					details["error_code"] = errorMap["errorcode"]
				}
			}
		}
	}

	message := fmt.Sprintf("绑卡尝试 #%d 成功 | CK_ID=%d", attemptNumber, walmartCKID)
	if !success {
		message = fmt.Sprintf("绑卡尝试 #%d 失败 | CK_ID=%d", attemptNumber, walmartCKID)
	}

	attemptStr := fmt.Sprintf("%d", attemptNumber)
	return p.recordLogWithDepartment(ctx, msg, "bind_attempt", "info", message, details, nil, nil, &durationMS, &attemptStr, &walmartCKID, departmentID)
}

// recordLog 通用日志记录方法
func (p *BindCardProcessor) recordLog(ctx context.Context, msg *BindCardMessage, logType, logLevel, message string,
	details map[string]interface{}, requestData, responseData interface{}, durationMS *float64, attemptNumber *string, walmartCKID *uint) error {
	return p.recordLogWithDepartment(ctx, msg, logType, logLevel, message, details, requestData, responseData, durationMS, attemptNumber, walmartCKID, nil)
}

// recordLogWithDepartment 通用日志记录方法（带部门ID）
func (p *BindCardProcessor) recordLogWithDepartment(ctx context.Context, msg *BindCardMessage, logType, logLevel, message string,
	details map[string]interface{}, requestData, responseData interface{}, durationMS *float64, attemptNumber *string, walmartCKID *uint, departmentID *uint) error {

	// 序列化数据
	var detailsStr, requestDataStr, responseDataStr *string

	if details != nil {
		if detailsJSON, err := json.Marshal(details); err == nil {
			str := string(detailsJSON)
			detailsStr = &str
		}
	}

	if requestData != nil {
		if requestJSON, err := json.Marshal(requestData); err == nil {
			str := string(requestJSON)
			requestDataStr = &str
		}
	}

	if responseData != nil {
		if responseJSON, err := json.Marshal(responseData); err == nil {
			str := string(responseJSON)
			responseDataStr = &str
		}
	}

	// 生成UUID
	id := generateUUID()

	// 创建绑卡日志记录
	merchantID := msg.MerchantID
	bindingLog := &model.BindingLog{
		ID:            id,
		CardRecordID:  msg.RecordID,
		MerchantID:    &merchantID,
		DepartmentID:  departmentID, // 使用传入的部门ID
		LogType:       logType,
		LogLevel:      logLevel,
		Message:       message,
		Details:       detailsStr,
		RequestData:   requestDataStr,
		ResponseData:  responseDataStr,
		DurationMS:    durationMS,
		AttemptNumber: attemptNumber,
		WalmartCKID:   walmartCKID,
		IPAddress:     nil, // TODO: 从上下文获取IP
		Timestamp:     time.Now(),
	}

	// 保存到数据库
	if err := p.db.WithContext(ctx).Create(bindingLog).Error; err != nil {
		return fmt.Errorf("保存绑卡日志失败: %w", err)
	}

	return nil
}

// generateLogID 生成短日志ID
func generateLogID() string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, 8)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}

// generateUUID 生成UUID - 使用标准UUID库确保线程安全和唯一性
func generateUUID() string {
	return uuid.New().String()
}

// updateCardRecordToFailed 更新卡记录状态为失败
func (p *BindCardProcessor) updateCardRecordToFailed(ctx context.Context, msg *BindCardMessage, err error) error {
	// 首先获取记录的创建时间以计算处理时长
	var record model.CardRecord
	if err := p.db.WithContext(ctx).Where("id = ?", msg.RecordID).First(&record).Error; err != nil {
		return fmt.Errorf("获取记录失败: %w", err)
	}

	// 计算处理时长（从创建时间到现在）
	processTime := time.Since(record.CreatedAt).Seconds()

	updateData := map[string]interface{}{
		"status":        "failed",
		"error_message": err.Error(),
		"process_time":  processTime,
		"updated_at":    time.Now(),
	}

	result := p.db.WithContext(ctx).Model(&model.CardRecord{}).
		Where("id = ?", msg.RecordID).
		Updates(updateData)

	if result.Error != nil {
		return fmt.Errorf("更新卡记录状态为失败时出错: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("没有找到要更新的记录，record_id: %s", msg.RecordID)
	}

	p.logger.Info("卡记录状态已更新为失败",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.String("error_message", err.Error()),
		zap.Float64("process_time", processTime),
		zap.Int64("rows_affected", result.RowsAffected))

	// 🔧 新增：检查是否为不可重试错误，如果是则发送失败回调消息
	p.logger.Info("检查是否为不可重试错误",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.String("error_message", err.Error()),
		zap.Bool("retry_strategy_available", p.retryStrategy != nil))

	if p.isNonRetryableError(err.Error()) {
		p.logger.Info("检测到不可重试错误，发送失败回调消息",
			zap.String("trace_id", msg.TraceID),
			zap.String("record_id", msg.RecordID),
			zap.String("error_message", err.Error()))

		if callbackErr := p.sendFailureCallback(ctx, msg, err.Error()); callbackErr != nil {
			p.logger.Error("发送失败回调消息失败",
				zap.String("trace_id", msg.TraceID),
				zap.String("record_id", msg.RecordID),
				zap.Error(callbackErr))
			// 注意：回调发送失败不影响状态更新的成功
		}
	} else {
		p.logger.Info("错误不属于不可重试错误，不发送回调消息",
			zap.String("trace_id", msg.TraceID),
			zap.String("record_id", msg.RecordID),
			zap.String("error_message", err.Error()))
	}

	return nil
}

// validateAmountAndSendCallback 验证金额并发送回调消息
func (p *BindCardProcessor) validateAmountAndSendCallback(ctx context.Context, msg *BindCardMessage, actualAmount int) error {
	p.logger.Info("开始金额验证和回调处理",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.Int("merchant_amount", msg.Amount),
		zap.Int("actual_amount", actualAmount))

	// 使用AmountValidatorService进行金额验证
	if err := p.amountValidator.ValidateAmount(ctx, msg.RecordID, msg.Amount, actualAmount); err != nil {
		p.logger.Warn("金额验证失败，发送失败回调消息",
			zap.String("trace_id", msg.TraceID),
			zap.String("record_id", msg.RecordID),
			zap.Error(err))

		// 金额验证失败，发送失败回调消息
		callbackMsg := &queue.CallbackMessage{
			RecordID:    msg.RecordID,
			MerchantID:  msg.MerchantID,
			Status:      "failed",
			Result:      map[string]interface{}{
				"error_code": "AMOUNT_MISMATCH",
				"error_message": "绑卡金额与真实金额不符",
				"merchant_amount": msg.Amount,
				"actual_amount": actualAmount,
			},
			CallbackURL: "", // 将在发送时从数据库获取
			RetryCount:  0,
			MaxRetries:  3,
		}

		if err := p.queueManager.PublishCallback(ctx, callbackMsg); err != nil {
			p.logger.Error("发送金额验证失败回调消息失败",
				zap.String("trace_id", msg.TraceID),
				zap.String("record_id", msg.RecordID),
				zap.Error(err))
			return fmt.Errorf("发送金额验证失败回调消息失败: %w", err)
		}

		p.logger.Info("金额验证失败回调消息已发送",
			zap.String("trace_id", msg.TraceID),
			zap.String("record_id", msg.RecordID))

		return nil // 回调消息发送成功，不返回错误
	}

	// 金额验证成功，发送成功回调消息
	p.logger.Info("金额验证成功，发送成功回调消息",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.Int("validated_amount", actualAmount))

	callbackMsg := &queue.CallbackMessage{
		RecordID:    msg.RecordID,
		MerchantID:  msg.MerchantID,
		Status:      "success",
		Result:      map[string]interface{}{
			"amount": actualAmount,
			"actual_amount": actualAmount,
			"validation_passed": true,
		},
		CallbackURL: "", // 将在发送时从数据库获取
		RetryCount:  0,
		MaxRetries:  3,
	}

	if err := p.queueManager.PublishCallback(ctx, callbackMsg); err != nil {
		p.logger.Error("发送成功回调消息失败",
			zap.String("trace_id", msg.TraceID),
			zap.String("record_id", msg.RecordID),
			zap.Error(err))
		return fmt.Errorf("发送成功回调消息失败: %w", err)
	}

	p.logger.Info("成功回调消息已发送",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID))

	return nil
}

// isNonRetryableError 检查是否为不可重试错误
func (p *BindCardProcessor) isNonRetryableError(errorMsg string) bool {
	if p.retryStrategy == nil {
		return false
	}

	retryConfig := p.config.RetryStrategy.BindCard
	for _, nonRetryableError := range retryConfig.NonRetryableErrors {
		if strings.Contains(errorMsg, nonRetryableError) {
			return true
		}
	}
	return false
}

// sendFailureCallback 发送失败回调消息
func (p *BindCardProcessor) sendFailureCallback(ctx context.Context, msg *BindCardMessage, errorMessage string) error {
	p.logger.Info("发送失败回调消息",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID),
		zap.String("error_message", errorMessage))

	callbackMsg := &queue.CallbackMessage{
		RecordID:    msg.RecordID,
		MerchantID:  msg.MerchantID,
		Status:      "failed",
		Result:      map[string]interface{}{
			"error_message": errorMessage,
			"error_type": "bind_card_failed",
		},
		CallbackURL: "", // 将在发送时从数据库获取
		RetryCount:  0,
		MaxRetries:  3,
	}

	if err := p.queueManager.PublishCallback(ctx, callbackMsg); err != nil {
		return fmt.Errorf("发送失败回调消息失败: %w", err)
	}

	p.logger.Info("失败回调消息已发送",
		zap.String("trace_id", msg.TraceID),
		zap.String("record_id", msg.RecordID))

	return nil
}


