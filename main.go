package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"walmart-bind-card-processor/internal/config"
	"walmart-bind-card-processor/internal/services"
)

var (
	Version   = "unknown"
	BuildTime = "unknown"
	GitCommit = "unknown"
	GitBranch = "unknown"
	BuildUser = "unknown"
	BuildHost = "unknown"
)

func main() {
	// 检查命令行参数
	if len(os.Args) > 1 {
		switch os.Args[1] {
		case "-version", "--version":
			printVersion()
			return
		case "validate-config":
			validateConfig()
			return
		case "-h", "--help":
			printHelp()
			return
		}
	}

	// 启动主处理器
	if err := startProcessor(); err != nil {
		fmt.Fprintf(os.Stderr, "Failed to start processor: %v\n", err)
		os.Exit(1)
	}
}

func printVersion() {
	fmt.Printf("Walmart Bind Card Processor\n")
	fmt.Printf("Version: %s\n", Version)
	fmt.Printf("Build Time: %s\n", BuildTime)
	fmt.Printf("Git Commit: %s\n", GitCommit)
	fmt.Printf("Git Branch: %s\n", GitBranch)
	fmt.Printf("Build User: %s\n", BuildUser)
	fmt.Printf("Build Host: %s\n", BuildHost)
}

func printHelp() {
	fmt.Printf("Walmart Bind Card Processor\n\n")
	fmt.Printf("Usage:\n")
	fmt.Printf("  %s [command]\n\n", os.Args[0])
	fmt.Printf("Available Commands:\n")
	fmt.Printf("  (no args)        Start the bind card processor\n")
	fmt.Printf("  validate-config  Validate configuration file\n")
	fmt.Printf("  -version         Show version information\n")
	fmt.Printf("  -h, --help       Show this help message\n")
	fmt.Printf("\nExamples:\n")
	fmt.Printf("  %s                    # Start processor\n", os.Args[0])
	fmt.Printf("  %s validate-config    # Validate config\n", os.Args[0])
	fmt.Printf("  %s -version           # Show version\n", os.Args[0])
}

func startProcessor() error {
	// 加载配置
	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	fmt.Printf("Starting Walmart Bind Card Processor v%s\n", Version)
	fmt.Printf("Build: %s, Commit: %s\n", BuildTime, GitCommit)
	fmt.Println("Configuration loaded successfully")

	// 创建服务容器
	serviceContainer, err := services.NewContainer(cfg)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to create service container: %v\n", err)
		os.Exit(1)
	}
	defer serviceContainer.Close()
	fmt.Println("Services initialized")

	// 执行启动验证
	if err := performStartupValidation(serviceContainer); err != nil {
		return fmt.Errorf("startup validation failed: %w", err)
	}
	fmt.Println("Startup validation completed")

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动服务管理器（包含连接监控和系统恢复服务）
	fmt.Println("Starting service manager...")
	if err := serviceContainer.ServiceManager.Start(ctx); err != nil {
		return fmt.Errorf("failed to start service manager: %w", err)
	}

	// 启动消息队列消费者
	fmt.Println("Starting message queue consumers...")

	// 启动绑卡消息消费者（根据配置开关决定是否启动）
	if cfg.Features.BindCard.Enabled {
		serviceContainer.Logger.Info("绑卡功能已启用，启动绑卡处理器")

		// 检查绑卡处理器是否可用
		if serviceContainer.BindCardProcessor == nil {
			serviceContainer.Logger.Error("BindCardProcessor not initialized")
			return fmt.Errorf("BindCardProcessor not available")
		}

		// 启动绑卡处理器（这会自动启动消息消费者和队列初始化）
		go func() {
			if err := serviceContainer.BindCardProcessor.Start(ctx); err != nil {
				serviceContainer.Logger.Errorf("绑卡处理器运行失败: %v", err)
			}
		}()

		serviceContainer.Logger.Info("绑卡处理器已启动，开始监听绑卡队列")
	} else {
		serviceContainer.Logger.Info("绑卡功能已禁用，跳过绑卡处理器启动")
	}



	fmt.Println("Processor started successfully")
	fmt.Println("Message queue consumers are now running...")

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	<-sigChan

	fmt.Println("Shutting down processor...")
	serviceContainer.Logger.Info("收到关闭信号，开始优雅关闭...")

	// 取消上下文，停止所有goroutine
	cancel()

	// 执行优雅关闭逻辑
	if err := gracefulShutdown(serviceContainer); err != nil {
		serviceContainer.Logger.Errorf("优雅关闭过程中发生错误: %v", err)
		fmt.Printf("Warning: Error during graceful shutdown: %v\n", err)
	}

	fmt.Println("Processor stopped")
	serviceContainer.Logger.Info("处理器已完全停止")
	return nil
}


// gracefulShutdown 执行优雅关闭逻辑
func gracefulShutdown(serviceContainer *services.Container) error {
	serviceContainer.Logger.Info("开始执行优雅关闭流程...")

	var lastErr error

	// 1. 停止接收新的消息（队列管理器会处理）
	serviceContainer.Logger.Info("停止消息队列消费者...")

	// 2. 等待当前正在处理的消息完成
	// 注意：这里可以添加一个超时机制，避免无限等待
	serviceContainer.Logger.Info("等待当前消息处理完成...")

	// 3. 停止服务管理器
	if serviceContainer.ServiceManager != nil {
		serviceContainer.Logger.Info("停止服务管理器...")
		serviceContainer.ServiceManager.Stop()
	}

	// 4. 关闭绑卡处理器（如果有清理逻辑）
	if serviceContainer.BindCardProcessor != nil {
		serviceContainer.Logger.Info("关闭绑卡处理器...")
		// 如果BindCardProcessor有Close方法，在这里调用
		// if err := serviceContainer.BindCardProcessor.Close(); err != nil {
		//     serviceContainer.Logger.Errorf("关闭绑卡处理器失败: %v", err)
		//     lastErr = err
		// }
	}

	// 5. 关闭性能监控器
	if serviceContainer.PerformanceMonitor != nil {
		serviceContainer.Logger.Info("关闭性能监控器...")
		// 如果PerformanceMonitor有Close方法，在这里调用
	}

	// 5. 刷新日志缓冲区
	if serviceContainer.ZapLogger != nil {
		serviceContainer.Logger.Info("刷新日志缓冲区...")
		if err := serviceContainer.ZapLogger.Sync(); err != nil {
			// 忽略sync错误，这在某些系统上是正常的
			serviceContainer.Logger.Debugf("日志同步警告: %v", err)
		}
	}

	// 6. 服务容器会在defer中自动关闭数据库、Redis、队列连接等
	serviceContainer.Logger.Info("优雅关闭流程完成")

	return lastErr
}



// performStartupValidation 执行启动验证
func performStartupValidation(serviceContainer *services.Container) error {
	serviceContainer.Logger.Info("开始执行启动验证...")

	// 1. 验证数据库连接
	if serviceContainer.DB != nil {
		serviceContainer.Logger.Info("验证数据库连接...")
		sqlDB, err := serviceContainer.DB.DB()
		if err != nil {
			return fmt.Errorf("获取数据库连接失败: %w", err)
		}

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := sqlDB.PingContext(ctx); err != nil {
			return fmt.Errorf("数据库连接测试失败: %w", err)
		}
		serviceContainer.Logger.Info("✅ 数据库连接正常")
	}

	// 2. 验证Redis连接
	if serviceContainer.Redis != nil {
		serviceContainer.Logger.Info("验证Redis连接...")
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if _, err := serviceContainer.Redis.Ping(ctx).Result(); err != nil {
			return fmt.Errorf("redis连接测试失败: %w", err)
		}
		serviceContainer.Logger.Info("✅ Redis连接正常")
	}

	// 3. 验证队列管理器
	if serviceContainer.QueueManager != nil {
		serviceContainer.Logger.Info("验证队列管理器...")
		// 队列管理器在创建时已经验证了连接
		serviceContainer.Logger.Info("✅ 队列管理器正常")
	}

	// 4. 验证关键服务
	if serviceContainer.BindCardProcessor == nil {
		return fmt.Errorf("绑卡处理器未初始化")
	}
	serviceContainer.Logger.Info("✅ 绑卡处理器已初始化")

	if serviceContainer.CKWeightManager == nil {
		return fmt.Errorf("CK权重管理器未初始化")
	}
	serviceContainer.Logger.Info("✅ CK权重管理器已初始化")

	serviceContainer.Logger.Info("启动验证完成，所有组件正常")
	return nil
}



// validateConfig 验证配置文件
func validateConfig() {
	fmt.Println("🔍 配置文件验证工具")
	fmt.Println("=" + strings.Repeat("=", 30))

	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		fmt.Printf("❌ 配置文件加载失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("✅ 配置文件加载成功")

	// 验证重试策略配置
	bindConfig := cfg.RetryStrategy.BindCard
	fmt.Printf("📋 绑卡重试配置:\n")
	fmt.Printf("  最大重试次数: %d\n", bindConfig.MaxAttempts)
	fmt.Printf("  可重试错误数量: %d\n", len(bindConfig.RetryableErrors))
	fmt.Printf("  CK切换错误数量: %d\n", len(bindConfig.CKSwitchErrors))
	fmt.Printf("  不可重试错误数量: %d\n", len(bindConfig.NonRetryableErrors))

	// 检查关键错误消息
	criticalErrors := []string{
		"绑卡金额与真实金额不符",
		"请先去登录",
		"您绑卡已超过单日20张限制",
	}

	fmt.Println("\n🔍 关键错误消息检查:")
	for _, critical := range criticalErrors {
		found := false
		for _, configured := range append(bindConfig.CKSwitchErrors, bindConfig.NonRetryableErrors...) {
			if strings.Contains(configured, critical) {
				found = true
				break
			}
		}
		if found {
			fmt.Printf("  ✅ %s\n", critical)
		} else {
			fmt.Printf("  ❌ 缺失: %s\n", critical)
		}
	}

	fmt.Println("\n🎉 配置验证完成!")
}

